import request, { requestCurrying } from '@/libs/request/request.ts'

export namespace AiModule {

  export type TextToSpeechRequest = {
    text: string
    voice_id: number
    volume: number
    speed: number
    pitch: number
  }

  export type TaskStatus = {
    result: {
      data: {
        srt_name: string;
        video_name: string;
      }
      /**
       * 该任务是什么功能
       */
      func: string;
    };
    /**
     * 状态：
     * PENDING: 任务正在执行
     * SUCCESS: 任务执行成功
     * FAILURE: 任务异常
     * REVOKED: 任务取消
     */
    status: 'PENDING' | 'SUCCESS' | 'FAILURE' | 'REVOKED';
    task_id: string;
  }

  export const endpoints = {
    textToSpeech: requestCurrying.post<TextToSpeechRequest, { task_id: string }>('/ai/api/synthesis'),

    queryTaskStatus: (task_id: string) => request.get<TaskStatus>(`/ai/api/status/${task_id}`),

    // __temp__getFileUrl(filename: string) {
    //   return ''
    // }
  }
}
