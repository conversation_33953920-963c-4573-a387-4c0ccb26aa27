import { useEditorContext } from '@rve/editor/contexts'
import React, { useCallback } from 'react'
import { Button } from '@/components/ui/button.tsx'
import useVirtualTabsStore from '@/libs/stores/useVirtualTabsStore.ts'

export function EditorHeader(): React.JSX.Element {
  const { scriptId, saveProject } = useEditorContext()

  const { pushNamedTab } = useVirtualTabsStore()

  const navigateToMixcutPage = useCallback(async () => {
    await saveProject()
    pushNamedTab('Mixcut', { id: scriptId })
  }, [scriptId, saveProject])

  return (
    <header
      className="sticky top-0 flex shrink-0 items-center gap-2.5
      bg-white dark:bg-gray-900/10
      border-l
      border-b border-gray-200 dark:border-gray-800
      p-1.5 px-4.5"
    >
      <div className="flex-grow flex items-center gap-3">
        <div className="text-sm text-gray-300">{scriptId}</div>
      </div>

      <Button variant="default" onClick={navigateToMixcutPage}>
        混剪预览
      </Button>
    </header>
  )
}
