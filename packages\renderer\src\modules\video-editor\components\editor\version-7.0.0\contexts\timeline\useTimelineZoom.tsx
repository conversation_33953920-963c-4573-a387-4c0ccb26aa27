import { useState, useCallback, RefObject, useEffect } from 'react'
import { ZOOM_CONSTRAINTS } from '../../constants'
import { clamp } from 'lodash'

type ZoomState = {
  scale: number
  scroll: number
}

export type TimelineZoomHook = {
  /** Current zoom level of the timeline */
  zoomScale: number

  /** Update the zoom scale */
  setZoomScale: (scale: number) => void

  /** Current horizontal scroll position */
  scrollPosition: number

  /** Update the scroll position */
  setScrollPosition: (position: number) => void

  /** Handle zoom interactions with delta and client X position */
  handleZoom: (delta: number, clientX: number) => void

  /** Handle zoom interactions from wheel events */
  handleWheelZoom: (event: WheelEvent) => void
}

/**
 * A custom hook that manages zoom and scroll behavior for a timeline component.
 * Handles both programmatic and wheel-based zooming while maintaining the zoom point
 * relative to the cursor position.
 */
export const useTimelineZoom = (timelineGridRef: RefObject<HTMLDivElement | null>): TimelineZoomHook => {
  const [zoomState, setZoomState] = useState<ZoomState>({
    scale: ZOOM_CONSTRAINTS.default,
    scroll: 0,
  })

  const clampScale = (scale: number) => clamp(
    scale,
    ZOOM_CONSTRAINTS.min,
    ZOOM_CONSTRAINTS.max,
  )

  const handleZoom = useCallback(
    (delta: number, clientX: number) => setZoomState(prevZoomState => {
      const scrollContainer = timelineGridRef.current?.parentElement
      if (!scrollContainer) return prevZoomState

      const { scale: prevScale } = prevZoomState

      const newScale = clampScale(prevScale + delta * ZOOM_CONSTRAINTS.step,)

      if (newScale === prevScale) return prevZoomState

      const rect = scrollContainer.getBoundingClientRect()
      const relativeX = clientX - rect.left + scrollContainer.scrollLeft
      const zoomFactor = newScale / prevScale
      const newScroll = relativeX * zoomFactor - (clientX - rect.left)

      requestAnimationFrame(() => {
        scrollContainer.scrollLeft = newScroll
      })

      return { scale: newScale, scroll: newScroll }
    }),
    [],
  )

  const handleWheelZoom = useCallback(
    (event: WheelEvent) => {
      if (event.ctrlKey || event.metaKey) {
        event.preventDefault()
        const delta = -Math.sign(event.deltaY) * ZOOM_CONSTRAINTS.wheelStep
        handleZoom(delta, event.clientX)
      }
    },
    [handleZoom],
  )

  useEffect(() => {
    const el = timelineGridRef.current
    if (!el) return

    el.addEventListener('wheel', handleWheelZoom, { passive: false })
    return () => el.removeEventListener('wheel', handleWheelZoom)
  }, [handleWheelZoom])

  return {
    zoomScale: zoomState.scale,
    scrollPosition: zoomState.scroll,
    setZoomScale: (newScale: number) => {
      return setZoomState(prev => ({ ...prev, scale: clampScale(newScale) }))
    },
    setScrollPosition: (newScroll: number) => {
      return setZoomState(prev => ({ ...prev, scroll: newScroll }))
    },
    handleZoom,
    handleWheelZoom,
  }
}
