import { useState, useEffect } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { useResource } from './useResource.tsx'

/**
 * Hook for managing resource loading state
 * Provides a synchronous interface for async loading state checks
 */
export const useResourceLoadingState = (type: ResourceType, url: string) => {
  const [isLoading, setIsLoading] = useState(false)
  const { isResourceLoading } = useResource()

  useEffect(() => {
    let isMounted = true

    const checkLoadingState = async () => {
      try {
        const loading = await isResourceLoading(type, url)
        if (isMounted) {
          setIsLoading(loading)
        }
      } catch (error) {
        console.error('检查资源加载状态失败:', error)
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    checkLoadingState()

    // 设置定期检查（可选，用于实时更新）
    const interval = setInterval(checkLoadingState, 1000)

    return () => {
      isMounted = false
      clearInterval(interval)
    }
  }, [type, url, isResourceLoading])

  return isLoading
}

/**
 * Hook for managing item loading state by ID
 */
export const useItemLoadingState = (id?: string | number) => {
  const [isLoading, setIsLoading] = useState(false)
  const { isItemLoading } = useResource()

  useEffect(() => {
    if (!id) {
      setIsLoading(false)
      return
    }

    let isMounted = true

    const checkLoadingState = async () => {
      try {
        const loading = await isItemLoading(id)
        if (isMounted) {
          setIsLoading(loading)
        }
      } catch (error) {
        console.error('检查项目加载状态失败:', error)
        if (isMounted) {
          setIsLoading(false)
        }
      }
    }

    checkLoadingState()

    // 设置定期检查（可选，用于实时更新）
    const interval = setInterval(checkLoadingState, 1000)

    return () => {
      isMounted = false
      clearInterval(interval)
    }
  }, [id, isItemLoading])

  return isLoading
}
