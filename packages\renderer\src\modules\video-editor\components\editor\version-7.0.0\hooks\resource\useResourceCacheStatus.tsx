import { useQuery, UseQueryOptions } from '@tanstack/react-query'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { useResource } from './useResource.tsx'

export interface ResourceCacheStatusResult {
  /**
   * 资源是否已缓存
   */
  isCached: boolean
  /**
   * 是否正在检查缓存状态
   */
  isChecking: boolean
  /**
   * 手动刷新缓存状态
   */
  refreshCacheStatus: () => void
}

/**
 * 生成缓存查询的 query key
 */
function getResourceCacheQueryKey(resourceType?: ResourceType, resourceUrl?: string) {
  return ['resource-cache', resourceType, resourceUrl]
}

/**
 * 可复用的资源缓存状态查询 Hook (使用 useQuery)
 *
 * @param resourceType 资源类型
 * @param resourceUrl 资源URL
 * @param options useQuery 配置选项
 * @returns 缓存状态对象
 */
export function useResourceCacheStatus(
  resourceType?: ResourceType,
  resourceUrl?: string,
  options: Omit<UseQueryOptions<boolean, Error>, 'queryKey' | 'queryFn'> & {
    /**
     * 查询间隔时间（毫秒），设置为 0 表示不自动刷新，默认为 0
     */
    refetchInterval?: number
  } = {}
): ResourceCacheStatusResult {
  const { isResourceCached } = useResource()

  const query = useQuery({
    queryKey: getResourceCacheQueryKey(resourceType, resourceUrl),
    queryFn: async () => {
      if (!resourceType || !resourceUrl) {
        return false
      }
      return await isResourceCached(resourceType, resourceUrl)
    },
    enabled: !!(resourceType && resourceUrl),
    staleTime: 30 * 1000, // 30秒内认为数据是新鲜的
    gcTime: 5 * 60 * 1000, // 5分钟后清理缓存
    retry: 1, // 失败时重试1次
    ...options
  })

  return {
    isCached: query.data ?? false,
    isChecking: query.isLoading || query.isFetching,
    refreshCacheStatus: query.refetch
  }
}

/**
 * 简化版本的缓存状态查询 Hook
 * 只进行一次查询，不包含定时刷新功能
 * 
 * @param resourceType 资源类型
 * @param resourceUrl 资源URL
 * @returns 缓存状态对象
 */
export function useResourceCacheStatusSimple(
  resourceType?: ResourceType,
  resourceUrl?: string
): Pick<ResourceCacheStatusResult, 'isCached' | 'isChecking'> {
  const { isCached, isChecking } = useResourceCacheStatus(resourceType, resourceUrl, {
    enabled: true,
    refetchInterval: 0,
    refetchOnMount: true
  })

  return { isCached, isChecking }
}
