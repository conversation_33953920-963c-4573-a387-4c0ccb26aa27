import { useState, useEffect, useRef, useCallback } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { useResource } from './useResource.tsx'

export interface ResourceCacheStatusResult {
  /**
   * 资源是否已缓存
   */
  isCached: boolean
  /**
   * 是否正在检查缓存状态
   */
  isChecking: boolean
  /**
   * 手动刷新缓存状态
   */
  refreshCacheStatus: () => void
}

/**
 * 可复用的资源缓存状态查询 Hook
 * 
 * @param resourceType 资源类型
 * @param resourceUrl 资源URL
 * @param options 配置选项
 * @returns 缓存状态对象
 */
export function useResourceCacheStatus(
  resourceType?: ResourceType,
  resourceUrl?: string,
  options: {
    /**
     * 是否启用自动查询，默认为 true
     */
    enabled?: boolean
    /**
     * 查询间隔时间（毫秒），设置为 0 表示不自动刷新，默认为 0
     */
    refetchInterval?: number
    /**
     * 是否在组件挂载时立即查询，默认为 true
     */
    refetchOnMount?: boolean
  } = {}
): ResourceCacheStatusResult {
  const {
    enabled = true,
    refetchInterval = 0,
    refetchOnMount = true
  } = options

  const { isResourceCached } = useResource()
  const [isCached, setIsCached] = useState(false)
  const [isChecking, setIsChecking] = useState(false)
  
  // 使用 ref 来跟踪组件是否已挂载，避免内存泄漏
  const isMountedRef = useRef(true)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  /**
   * 检查缓存状态的核心逻辑
   */
  const checkCacheStatus = useCallback(async () => {
    // 参数验证
    if (!enabled || !resourceType || !resourceUrl) {
      setIsCached(false)
      setIsChecking(false)
      return
    }

    // 避免重复检查
    if (isChecking) {
      return
    }

    setIsChecking(true)

    try {
      const cached = await isResourceCached(resourceType, resourceUrl)
      
      // 检查组件是否仍然挂载
      if (isMountedRef.current) {
        setIsCached(cached)
      }
    } catch (error) {
      console.error('[useResourceCacheStatus] 检查资源缓存状态失败:', error)
      console.error('[useResourceCacheStatus] 参数:', { resourceType, resourceUrl })
      
      // 检查组件是否仍然挂载
      if (isMountedRef.current) {
        setIsCached(false)
      }
    } finally {
      // 检查组件是否仍然挂载
      if (isMountedRef.current) {
        setIsChecking(false)
      }
    }
  }, [enabled, resourceType, resourceUrl, isResourceCached, isChecking])

  /**
   * 手动刷新缓存状态
   */
  const refreshCacheStatus = useCallback(() => {
    checkCacheStatus()
  }, [checkCacheStatus])

  // 初始查询和依赖变化时的查询
  useEffect(() => {
    if (refetchOnMount) {
      checkCacheStatus()
    }
  }, [checkCacheStatus, refetchOnMount])

  // 定时刷新逻辑
  useEffect(() => {
    // 清除之前的定时器
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }

    // 如果设置了刷新间隔且大于0，则启动定时器
    if (refetchInterval > 0 && enabled && resourceType && resourceUrl) {
      intervalRef.current = setInterval(() => {
        checkCacheStatus()
      }, refetchInterval)
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [refetchInterval, enabled, resourceType, resourceUrl, checkCacheStatus])

  // 组件卸载时的清理
  useEffect(() => {
    return () => {
      isMountedRef.current = false
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
        intervalRef.current = null
      }
    }
  }, [])

  return {
    isCached,
    isChecking,
    refreshCacheStatus
  }
}

/**
 * 简化版本的缓存状态查询 Hook
 * 只进行一次查询，不包含定时刷新功能
 * 
 * @param resourceType 资源类型
 * @param resourceUrl 资源URL
 * @returns 缓存状态对象
 */
export function useResourceCacheStatusSimple(
  resourceType?: ResourceType,
  resourceUrl?: string
): Pick<ResourceCacheStatusResult, 'isCached' | 'isChecking'> {
  const { isCached, isChecking } = useResourceCacheStatus(resourceType, resourceUrl, {
    enabled: true,
    refetchInterval: 0,
    refetchOnMount: true
  })

  return { isCached, isChecking }
}
