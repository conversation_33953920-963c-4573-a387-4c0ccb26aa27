import React, { useState } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { StarIcon, Loader2Icon } from 'lucide-react'
import { cn } from '@/components/lib/utils'
import { useToggleResourceCollection } from '@/hooks/queries/useQueryResourceCollection'
import { toast } from '@/hooks/useToast.ts'

export interface ResourceCollectionIndicatorProps {
  /**
   * 资源类型
   */
  resourceType: ResourceType
  /**
   * 资源ID
   */
  resourceId: string | number
  /**
   * 是否已收藏
   */
  isCollected?: boolean
  /**
   * 是否显示为文本而不是图标
   */
  asText?: boolean
  /**
   * 图标大小
   */
  size?: number
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 点击收藏按钮后的回调
   */
  onCollectionChange?: (collected: boolean) => void
}

/**
 * 获取资源类型的中文名称
 * @param resourceType 资源类型
 * @returns 资源类型的中文名称
 */
function getResourceTypeName(resourceType: ResourceType): string {
  switch (resourceType) {
    case ResourceType.STICKER:
      return '贴纸'
    case ResourceType.MUSIC:
      return '音乐'
    case ResourceType.SOUND:
      return '音效'
    case ResourceType.FONT:
      return '字体'
    case ResourceType.AUDIO:
      return '音频'
    default:
      return '资源'
  }
}

/**
 * 资源收藏状态指示器
 * 显示资源是否已被收藏，并允许用户收藏/取消收藏
 */
export function ResourceCollectionIndicator({
  resourceType,
  resourceId,
  isCollected = false,
  asText = false,
  size = 16,
  className,
  onCollectionChange,
}: ResourceCollectionIndicatorProps) {
  // 简化状态管理：只使用本地状态和传入的 isCollected
  const [localCollected, setLocalCollected] = useState(isCollected)

  // 使用 useMutation 更新收藏状态
  const toggleCollection = useToggleResourceCollection()

  // 当传入的 isCollected 改变时，同步本地状态
  if (localCollected !== isCollected) {
    setLocalCollected(isCollected)
  }

  // 使用本地状态作为显示状态
  const collected = localCollected

  // 处理收藏按钮点击
  const handleToggleCollection = async (e: React.MouseEvent) => {
    e.stopPropagation()

    // 切换收藏状态
    const newCollectionState = !collected
    const resourceTypeName = getResourceTypeName(resourceType)

    // 立即更新本地状态（乐观更新）
    setLocalCollected(newCollectionState)

    try {
      // 调用 mutation 更新收藏状态
      await toggleCollection.mutateAsync({
        resourceType,
        resourceId,
        collected: newCollectionState
      })

      // 操作成功提示
      toast({
        title: newCollectionState ? '收藏成功' : '取消收藏成功',
        description: newCollectionState
          ? `${resourceTypeName}已添加到您的收藏`
          : `${resourceTypeName}已从收藏中移除`,
        duration: 2000,
      })

      // 如果提供了回调，则调用它
      if (onCollectionChange) {
        onCollectionChange(newCollectionState)
      }
    } catch (error) {
      console.error('更新收藏状态失败:', error)

      // 发生错误时回滚本地状态
      setLocalCollected(!newCollectionState)

      // 显示错误提示
      toast({
        title: `${newCollectionState ? '收藏' : '取消收藏'}失败`,
        description: `${resourceTypeName}${newCollectionState ? '收藏' : '取消收藏'}操作未能完成，请稍后再试`,
        variant: 'destructive',
        duration: 3000,
      })
    }
  }

  if (asText) {
    if (toggleCollection.isPending) return <span className={className}>更新中...</span>
    if (collected) return <span className={className}>已收藏</span>
    return <span className={className}>未收藏</span>
  }

  return (
    <div
      className={cn(
        'flex items-center justify-center',
        className
      )}
    >
      {toggleCollection.isPending ? (
        <Loader2Icon
          className="animate-spin text-gray-400"
          style={{ width: size, height: size }}
        />
      ) : (
        <button
          onClick={handleToggleCollection}
          className="bg-gray-900/80 rounded p-1 cursor-pointer text-gray-500 hover:bg-gray-900/50 transition-all "
          aria-label={collected ? '取消收藏' : '收藏'}
          title={collected ? '取消收藏' : '收藏'}
        >
          <StarIcon
            className={cn(
              'transition-all',
              collected
                ? 'text-yellow-400 fill-yellow-400'
                : 'text-gray-400 hover:text-yellow-400'
            )}
            style={{ width: size, height: size }}
          />
        </button>
      )}
    </div>
  )
}
