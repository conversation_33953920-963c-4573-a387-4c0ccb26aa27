import React from 'react'
import { StickerOverlay } from '@app/rve-shared/types'
import { AnimationSettings } from '../../shared/animation-preview'
import { animationTemplates } from '@app/rve-shared/constants'
import { useOverlayEditing } from '@rve/editor/contexts'

export const ImageSettingsPanel: React.FC = () => {
  const { localOverlay: stickerOverlay, requestUpdate: updateOverlay } = useOverlayEditing<StickerOverlay>()

  const handleEnterAnimationSelect = (animationKey: string) => {
    updateOverlay({
      styles: {
        animation: {
          ...stickerOverlay.styles.animation,
          enter: animationKey === 'none' ? undefined : animationKey,
        },

      }
    }, true)
  }

  const handleExitAnimationSelect = (animationKey: string) => {
    updateOverlay({
      styles: {
        animation: {
          ...stickerOverlay.styles.animation,
          exit: animationKey === 'none' ? undefined : animationKey,
        },
      }
    }, true)
  }

  return (
    <div className="space-y-6">
      <AnimationSettings
        animations={animationTemplates}
        selectedEnterAnimation={stickerOverlay.styles.animation?.enter}
        selectedExitAnimation={stickerOverlay.styles.animation?.exit}
        onEnterAnimationSelect={handleEnterAnimationSelect}
        onExitAnimationSelect={handleExitAnimationSelect}
      />
    </div>
  )
}
