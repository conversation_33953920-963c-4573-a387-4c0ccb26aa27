import React, { useCallback, useEffect, useRef, useState } from 'react'
import { FPS, PIXELS_PER_FRAME } from '../../constants'
import { clamp } from 'lodash'
import { cn } from '@/components/lib/utils.ts'
import { useEditorContext, useTimeline } from '@rve/editor/contexts'

/**
 * Renders timeline markers with adaptive scaling based on zoom level
 * Displays time indicators and clickable markers for timeline navigation
 */
export const TimelineRuler = (): React.JSX.Element => {
  const { durationInFrames, videoPlayer: { seekTo } } = useEditorContext()
  const { zoomScale } = useTimeline()

  // Ref to measure the actual container width
  const containerRef = useRef<HTMLDivElement>(null)
  const [containerWidth, setContainerWidth] = useState<number>(0)

  // Effect to measure and track container width changes with optimizations
  useEffect(() => {
    const updateContainerWidth = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect()
        const width = rect.width
        // Only update if width actually changed to avoid unnecessary re-renders
        setContainerWidth(prevWidth => {
          if (Math.abs(prevWidth - width) > 1) { // 1px tolerance to avoid micro-changes
            return width
          }
          return prevWidth
        })
      }
    }

    // Set up resize observer to track width changes with debouncing
    const resizeObserver = new ResizeObserver(entries => {
      // Use requestAnimationFrame to debounce rapid resize events
      requestAnimationFrame(() => {
        for (const entry of entries) {
          const width = entry.contentRect.width
          setContainerWidth(prevWidth => {
            if (Math.abs(prevWidth - width) > 1) { // 1px tolerance
              return width
            }
            return prevWidth
          })
        }
      })
    })

    if (containerRef.current) {
      resizeObserver.observe(containerRef.current)
      updateContainerWidth()
    }

    return () => {
      resizeObserver.disconnect()
    }
  }, []) // No dependencies to avoid unnecessary re-runs

  const generateMarkers = useCallback(
    (): React.JSX.Element[] => {
      const markers: React.JSX.Element[] = []
      // Calculate total seconds more precisely using frames
      const totalSeconds = durationInFrames / FPS

      // Calculate the minimum visible duration based on actual container width
      // Use pixel-to-time conversion: width_pixels / (zoomScale * PIXELS_PER_FRAME) / FPS = duration_seconds
      const minVisibleDurationSeconds = (containerWidth / (zoomScale * PIXELS_PER_FRAME)) / FPS
      const effectiveSeconds = Math.max(totalSeconds, minVisibleDurationSeconds)

      // Dynamic interval calculation based on zoom level
      const baseInterval = ((): number => {
        const targetMarkerCount = clamp(
          Math.floor(25 * zoomScale),
          8,
          40
        )
        // Use effective seconds to ensure proper marker distribution across the visible area
        const rawInterval = effectiveSeconds / targetMarkerCount

        const niceIntervals: number[] = [
          0.04, 0.08, 0.1, 0.2, 0.25, 0.5, 1, 2, 5, 10, 15, 30, 60, 120, 300,
        ]

        if (isNaN(rawInterval) || rawInterval <= 0) {
          return niceIntervals[0]
        }

        return niceIntervals.reduce((prev, curr) =>
          Math.abs(curr - rawInterval) < Math.abs(prev - rawInterval)
            ? curr
            : prev,
        )
      })()

      // Calculate sub-intervals for different marker types
      const majorInterval = baseInterval
      const minorInterval = baseInterval / 4
      const microInterval = baseInterval / 8
      const labelInterval = majorInterval

      // Safeguard against division by zero or excessively small microInterval
      if (microInterval < 1e-6) {
        if (totalSeconds > 1e-9) {
          const zeroMarker: React.JSX.Element = (
            <div
              key="0s-marker-guard"
              className="absolute top-0 flex flex-col items-center"
              data-timeline-marker="tick"
              style={{
                left: '0px',
                transform: 'translateX(-50%)',
              }}
            >
              <div
                className="h-2 w-[1px] bg-gray-300 dark:bg-gray-600/50"
                data-timeline-marker="indicator"
              />
              <span
                className="text-[8px] font-light tracking-tight text-gray-700 dark:text-gray-300/90 mt-0.5 select-none"
                data-timeline-marker="label"
              >
                0.00s
              </span>
            </div>
          )
          return [zeroMarker]
        }
        return [] // No duration or too small interval, no markers
      }

      // Calculate number of micro steps for each interval type. These should be integers.
      const numMicroStepsInMajor = Math.round(majorInterval / microInterval)
      const numMicroStepsInMinor = Math.round(minorInterval / microInterval)
      const numMicroStepsInLabel = Math.round(labelInterval / microInterval)

      // Use effectiveSeconds to ensure markers cover the entire visible area
      const effectiveTotalSeconds = Math.max(0, effectiveSeconds)
      const totalMicroSteps = Math.floor(effectiveTotalSeconds / microInterval)

      const epsilon = 1e-9 // Small value for floating point comparisons

      for (let i = 0; i <= totalMicroSteps; i++) {
        const timeCandidate = i * microInterval

        if (timeCandidate > effectiveTotalSeconds + epsilon && i > 0) {
          continue
        }
        const currentTime = Math.min(timeCandidate, effectiveTotalSeconds)

        const preMinutes = Math.floor(currentTime / 60)
        const preSeconds = currentTime % 60

        const resolvedSeconds = Math.abs(preSeconds - 60) < epsilon ? 0 : preSeconds
        const resolvedMinutes = Math.abs(preSeconds - 60) < epsilon && preMinutes < Number.MAX_SAFE_INTEGER
          ? preMinutes + 1
          : preMinutes

        const isMajorTick = i % numMicroStepsInMajor === 0
        const isMinorTick = i % numMicroStepsInMinor === 0
        const shouldShowLabel = i % numMicroStepsInLabel === 0

        // Convert time to pixels: 秒->帧->像素
        const currentFrame = currentTime * FPS
        const positionPx = currentFrame * zoomScale * PIXELS_PER_FRAME

        const isZero = resolvedMinutes === 0 && resolvedSeconds === 0

        const markerElement: React.JSX.Element = (
          <div
            key={i}
            className="absolute top-0 flex flex-col items-center"
            data-timeline-marker="tick"
            style={{
              left: `${positionPx}px`,
              transform: 'translateX(-50%)',
            }}
          >
            <div
              data-timeline-marker="indicator"
              className={cn(
                `transition-all duration-150 ease-in-out
              group-hover:bg-blue-500/50 dark:group-hover:bg-blue-300/50`,
                isMajorTick
                  ? 'h-2 w-[1px] bg-gray-300 dark:bg-gray-600/50'
                  : isMinorTick
                    ? 'h-1 w-px bg-gray-300 dark:bg-gray-600/40'
                    : 'h-0.5 w-px bg-gray-200 dark:bg-gray-600/30'
              )}
            />
            {shouldShowLabel && (
              <span
                className={cn(
                  `text-[8px] font-light tracking-tight
                 text-gray-700 dark:text-gray-300/90
                   mt-0.5 select-none
                   duration-150`,
                  isZero && 'pl-2'
                )}
                data-timeline-marker="label"
              >
                {
                  isZero
                    ? '0'
                    : `${resolvedMinutes}:${resolvedSeconds.toFixed().padStart(2, '0')}`
                }
              </span>
            )}
          </div>
        )

        markers.push(markerElement)
      }

      return markers
    },
    [containerWidth, durationInFrames, zoomScale]
  )

  /**
   * Handles click events on the timeline
   * Calculates the relative position of the click and calls the handler
   * Only processes clicks that originated on the timeline markers, not bubbled from items
   */
  const handleClick = useCallback(
    (event: React.MouseEvent<HTMLDivElement>) => {
      // Only process clicks that originated on the timeline itself, not from items
      if (
        event.target === event.currentTarget
        || (event.target as HTMLElement).closest('[data-timeline-marker]')
      ) {
        const { left, width } = event.currentTarget.getBoundingClientRect()
        const clickPosition = (event.clientX - left) / width
        // Convert click position to frame-accurate position
        const framePosition = Math.round(clickPosition * durationInFrames) / durationInFrames
        seekTo(framePosition / PIXELS_PER_FRAME)
      }
    },
    [durationInFrames],
  )

  return (
    <div
      ref={containerRef}
      className="fixed top-0 left-0 right-0 h-6 cursor-pointer z-10 overflow-x-hidden"
      data-timeline-marker="root"
      onClick={handleClick}
    >
      {generateMarkers()}
    </div>
  )
}
