import { SubCacheManager } from '../types'
import opentype from 'opentype.js'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'

export class FontCacheManager extends SubCacheManager {

  private memoryCache: Map<string, opentype.Font> = new Map()

  public async cacheFont(src: string) {
    if (this.memoryCache.has(src)) return

    let font: opentype.Font

    // 检查是否是本地字体文件（public 目录中的字体）
    if (this.isLocalFont(src)) {
      font = await opentype.load(src)
    } else {
      // 处理远程字体文件
      const localCachedUrl = await window.resource.getPath({
        url: src,
        type: ResourceType.FONT
      })

      font = localCachedUrl
        ? await opentype.load(`file://${localCachedUrl}`)
        : await opentype.load(src)
    }

    this.memoryCache.set(src, font)
    console.debug(`[font.cache] 已将字体${font.names.fontFamily?.zh || font.names.fontFamily?.en || '未知字体'}缓存到内存`)
  }

  /**
   * 判断是否是本地字体文件（public 目录中的字体）
   * @param src 字体文件路径
   * @returns 是否是本地字体
   */
  private isLocalFont(src: string): boolean {
    // 检查是否是以 /fonts/ 或 ./fonts/ 开头的本地字体路径
    return src.startsWith('/fonts/') || src.startsWith('./fonts/')
  }

  public getFont(src: string) {
    return this.memoryCache.get(src) || null
  }

  cleanup(_now: number, _maxAge: number) {
    this.memoryCache.clear()
  }
}
