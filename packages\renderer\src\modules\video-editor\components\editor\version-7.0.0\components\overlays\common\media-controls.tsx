import React from 'react'
import { FormSlider, SectionTitle } from './form-components'
import { BaseOverlay } from '@app/rve-shared/types'
import { FPS } from '../../../constants'

/**
 * 媒体控制组件的 Props 接口
 * 支持任何继承自 BaseOverlay 且具有 speed 属性的类型
 */
interface MediaControlsProps<T extends BaseOverlay & { speed?: number }> {
  /**
   * 当前 overlay 对象
   */
  overlay: T

  /**
   * 更新 overlay 属性的回调函数
   */
  onOverlayPropertyChange: (updates: Partial<T>, commit?: boolean) => void

  /**
   * 变速处理函数（可选）
   * 如果提供，将用于处理变速逻辑；否则使用默认的属性更新
   */
  onSpeedChange?: (speed: number, newDuration: number) => void

  /**
   * 变速的最小值，默认为 0.25
   */
  minSpeed?: number

  /**
   * 变速的最大值，默认为 4
   */
  maxSpeed?: number

  /**
   * 变速的步长，默认为 0.25
   */
  speedStep?: number

  /**
   * 时长的最小值（秒），默认为 0.1
   */
  minDuration?: number

  /**
   * 时长的最大值（秒），默认为 60
   */
  maxDuration?: number

  /**
   * 时长的步长（秒），默认为 0.1
   */
  durationStep?: number

  /**
   * 是否显示变速控制，默认为 true
   */
  showSpeedControl?: boolean

  /**
   * 是否显示时长控制，默认为 true
   */
  showDurationControl?: boolean
}

/**
 * 变速控制组件
 *
 * 提供媒体变速调节功能，支持重置和实时数值显示
 *
 * @template T - 继承自 BaseOverlay 且具有 speed 属性的 overlay 类型
 * @param props - 组件属性
 * @returns 变速控制 UI
 */
export function SpeedControl<T extends BaseOverlay & { speed?: number }>({
  overlay,
  onOverlayPropertyChange,
  onSpeedChange,
  minSpeed = 0.25,
  maxSpeed = 4,
  speedStep = 0.25
}: Pick<MediaControlsProps<T>, 'overlay' | 'onOverlayPropertyChange' | 'onSpeedChange' | 'minSpeed' | 'maxSpeed' | 'speedStep'>) {
  /**
   * 变速处理函数
   */
  const handleSpeedChange = (newSpeed: number, commit?: boolean) => {
    if (onSpeedChange) {
      // 使用自定义的变速处理逻辑
      const baseDuration = overlay.durationInFrames * (overlay.speed ?? 1)
      const newDuration = Math.round(baseDuration / newSpeed)
      onSpeedChange(newSpeed, newDuration)
    } else {
      // 使用默认的属性更新
      const baseDuration = overlay.durationInFrames * (overlay.speed ?? 1)
      const newDuration = Math.round(baseDuration / newSpeed)
      onOverlayPropertyChange({
        speed: newSpeed,
        durationInFrames: newDuration
      } as Partial<T>, commit)
    }
  }

  return (
    <div className="overlay-setting-card">
      <div className="flex items-center justify-between">
        <SectionTitle title={`变速 (*${overlay?.speed ?? 1})`} />
        <button
          onClick={() => handleSpeedChange(1)}
          className={`text-xs px-2.5 py-1.5 rounded-md transition-colors ${
            (overlay?.speed ?? 1) !== 1
              ? 'bg-primary/20 text-primary hover:bg-primary/30'
              : 'text-muted-foreground '
          }`}
        >
          重置
        </button>
      </div>
      <FormSlider
        showInput={false}
        min={minSpeed}
        max={maxSpeed}
        step={speedStep}
        value={overlay?.speed ?? 1}
        onChange={(val, commit) => handleSpeedChange(val, commit)}
      />
      <div className="flex justify-between text-xs text-muted-foreground">
        <span>0.25</span>
        <span>4</span>
      </div>
    </div>
  )
}

/**
 * 时长控制组件
 *
 * 提供媒体时长调节功能，支持实时数值显示
 *
 * @template T - 继承自 BaseOverlay 的 overlay 类型
 * @param props - 组件属性
 * @returns 时长控制 UI
 */
export function DurationControl<T extends BaseOverlay>({
  overlay,
  onOverlayPropertyChange,
  minDuration = 0.1,
  maxDuration = 60,
  durationStep = 0.1
}: Pick<MediaControlsProps<T>, 'overlay' | 'onOverlayPropertyChange' | 'minDuration' | 'maxDuration' | 'durationStep'>) {
  return (
    <div className="overlay-setting-card">
      <div className="flex items-center justify-between">
        <SectionTitle title="时长" />
        <span className="text-xs font-medium text-muted-foreground px-2 py-1 rounded">
          {((overlay?.durationInFrames ?? 0) / FPS).toFixed(2)}秒
        </span>
      </div>
      <FormSlider
        showInput={false}
        min={minDuration}
        max={maxDuration}
        step={durationStep}
        value={overlay?.durationInFrames / FPS}
        onChange={(val, commit) => onOverlayPropertyChange({ durationInFrames: Math.round(val * FPS) } as Partial<T>, commit)}
      />
    </div>
  )
}

/**
 * 媒体控制组件
 *
 * 集成变速和时长控制功能的复合组件
 *
 * @template T - 继承自 BaseOverlay 且具有 speed 属性的 overlay 类型
 * @param props - 组件属性
 * @returns 媒体控制 UI
 */
export function MediaControls<T extends BaseOverlay & { speed?: number }>({
  overlay,
  onOverlayPropertyChange,
  onSpeedChange,
  minSpeed = 0.25,
  maxSpeed = 4,
  speedStep = 0.25,
  minDuration = 0.1,
  maxDuration = 60,
  durationStep = 0.1,
  showSpeedControl = true,
  showDurationControl = true
}: MediaControlsProps<T>) {
  return (
    <>
      {showSpeedControl && (
        <SpeedControl
          overlay={overlay}
          onOverlayPropertyChange={onOverlayPropertyChange}
          onSpeedChange={onSpeedChange}
          minSpeed={minSpeed}
          maxSpeed={maxSpeed}
          speedStep={speedStep}
        />
      )}

      {showDurationControl && (
        <DurationControl
          overlay={overlay}
          onOverlayPropertyChange={onOverlayPropertyChange}
          minDuration={minDuration}
          maxDuration={maxDuration}
          durationStep={durationStep}
        />
      )}
    </>
  )
}
