import React, { use<PERSON><PERSON><PERSON>, useMemo, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ArrowDownAz, CirclePlus, Edit3, Ellipsis, Folder, Search, Trash } from 'lucide-react'
import { NavLink, useLocation, useParams } from 'react-router'
import { cn } from '@/components/lib/utils'
import { useInfiniteQueryProjectList } from '@/hooks/queries/useQueryProject'
import { Project } from '@/types/project'
import { useQueryClient } from '@tanstack/react-query'
import { useModal, useModalContext } from '@/libs/tools/modal.tsx'
import { genForm } from '@/libs/tools/form.tsx'
import { z } from 'zod'
import { ModalFooter, ModalHeader } from '@/components/modal'
import { ResourceModule } from '@/libs/request/api/resource'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { useFormContext } from 'react-hook-form'
import { useDeleteModal } from '@/components/modal/delete'
import { Badge } from '@/components/ui/badge'

const ProjectFrom = genForm(
  z.object({
    name: z.string().min(1, '请输入项目名称'),
  }),
  {
    fields: {
      name: {
        label: '项目名称',
        render: ({ field }) => {
          const { setValue } = useFormContext()

          return (
            <div className="relative">
              <Input
                {...field}
                className="pr-16"
                onChange={e => setValue('name', e.currentTarget.value.slice(0, 20))}
              />
              <span className="absolute top-1/2 -translate-y-1/2 right-2 text-muted-foreground text-sm">
                {field.value.length}/20
              </span>
            </div>
          )
        },
      },
    },
  },
)

function ProjectModal({
  title,
  action,
  name = '',
}: {
  title: string
  action: (name: string) => Promise<any>
  name?: string
}) {
  const { close } = useModalContext()
  const [pending, setPending] = useState(false)

  return (
    <>
      <ModalHeader title={title} />
      <ProjectFrom
        defaultValues={{ name }}
        onSubmit={async data => {
          setPending(true)
          try {
            await action(data.name)
            close()
          } finally {
            setPending(false)
          }
        }}
      >
        <ModalFooter pending={pending} />
      </ProjectFrom>
    </>
  )
}

function useAddProject() {
  const modal = useModal()
  const queryClient = useQueryClient()
  return useCallback(
    () =>
      modal({
        content: (
          <ProjectModal
            title="创建项目"
            action={async name => {
              await ResourceModule.project.create({ projectName: name })
              queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PROJECT_LIST] })
            }}
          />
        ),
      }),
    [],
  )
}

function useEditProject(id: number) {
  const modal = useModal()
  const queryClient = useQueryClient()
  return useCallback(
    (projectName: string) =>
      modal({
        content: (
          <ProjectModal
            title="编辑项目"
            name={projectName}
            action={async name => {
              await ResourceModule.project.update({ id, projectName: name })
              queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PROJECT_LIST] })
            }}
          />
        ),
      }),
    [id],
  )
}

function Item({ project }: { project: Project }) {
  const editProject = useEditProject(project.id)
  const deleteModal = useDeleteModal()
  const queryClient = useQueryClient()
  const params = useParams()
  const location = useLocation()
  const path = useMemo(
    () => location.pathname.replace(`projects/${params.projectId}`, `projects/${project.id}`),
    [params, location.pathname, project.id],
  )

  return (
    <NavLink
      to={path}
      onClick={e => {
        if (e.currentTarget.classList.contains('active')) {
          e.preventDefault()
        }
      }}
      className={({ isActive }) => {
        return cn(
          'group flex items-center gap-1 rounded-md h-10 p-2 bg-muted-foreground/10 border-2 border-transparent',
          isActive && 'active border-border',
        )
      }}
      draggable={false}
    >
      <Folder className="size-4" />
      <span className="leading-0 text-sm">{project.projectName}</span>
      <Badge
        className="ml-auto rounded-sm border border-border
          bg-muted-foreground/10 hover:bg-muted-foreground/10 text-white
          group-[.active]:bg-gray-500 group-[.active]:border-gray-500"
      >
        通用
      </Badge>
      <div className="group/inner relative hidden group-hover:flex">
        <Button size="icon" variant="ghost" className="size-6">
          <Ellipsis className="size-4" />
        </Button>
        <div
          className="absolute right-0 top-full w-30 bg-background shadow-lg border rounded-md
            opacity-0 scale-50 origin-top-right pointer-events-none
            group-hover/inner:opacity-100 group-hover/inner:scale-100 group-hover/inner:pointer-events-auto
            transition-all duration-200 ease-out flex flex-col p-1 gap-0.5"
          onClick={e => e.preventDefault()}
        >
          <Button
            variant="ghost"
            size="sm"
            className="flex justify-start gap-3"
            onClick={() => editProject(project.projectName)}
          >
            <Edit3 className="size-4" />
            重命名
          </Button>
          <Button
            variant="ghost"
            size="sm"
            className="flex justify-start gap-3 hover:bg-destructive/40"
            onClick={() => {
              return deleteModal({
                kind: '项目',
                name: project.projectName,
                danger: true,
                action: async () => {
                  await ResourceModule.project.delete({ id: project.id })
                  await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.PROJECT_LIST] })
                },
              })
            }}
          >
            <Trash className="size-4" />
            删除
          </Button>
        </div>
      </div>
    </NavLink>
  )
}

export function Sidebar() {
  const addProject = useAddProject()
  const [keyword, setKeyword] = useState('')
  const { data } = useInfiniteQueryProjectList({ keyword })
  const projects = useMemo(() => data?.pages.flatMap(page => page.list) || [], [data])

  return (
    <div className="flex flex-col gap-4 h-full pt-4">
      <div className="flex items-center gap-1 px-4">
        <span className="font-bold">我的项目({projects.length})</span>
        <Button size="icon" variant="ghost">
          <ArrowDownAz className="size-4.5" />
        </Button>
        <Button size="icon" variant="ghost" className="ml-auto" onClick={addProject}>
          <CirclePlus className="size-4.5" />
        </Button>
      </div>
      <div className="px-4 relative">
        <Input
          placeholder="关键词搜索"
          className="h-10 pr-10"
          value={keyword}
          onChange={e => setKeyword(e.target.value)}
        />
        <Search className="absolute size-4 top-1/2 -translate-y-1/2 right-7" />
      </div>
      <div className="px-4 pb-4 flex flex-col gap-3 flex-1 min-h-0 overflow-x-hidden overflow-y-hidden">
        {projects.map(project => (
          <Item key={project.id} project={project} />
        ))}
      </div>
    </div>
  )
}
