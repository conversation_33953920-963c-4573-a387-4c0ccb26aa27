import { ResourceType } from '@app/shared/types/resource-cache.types'
import { SubCacheManager } from '../types'

// 加载状态数据结构
export interface LoadingState {
  isLoading: boolean
  timestamp: number
}

export class LoadingStateCacheManager extends SubCacheManager {

  /**
   * 生成加载状态键
   */
  private getLoadingStateKey(type: ResourceType, url: string): string {
    return `${type}:${url}`
  }

  /**
   * 设置资源加载状态
   */
  async setResourceLoadingState(type: ResourceType, url: string, isLoading: boolean): Promise<void> {
    const key = this.getLoadingStateKey(type, url)
    const state: LoadingState = {
      isLoading,
      timestamp: Date.now()
    }
    await this.store.setItem(key, state)
  }

  /**
   * 获取资源加载状态
   */
  async getResourceLoadingState(type: ResourceType, url: string): Promise<boolean> {
    const key = this.getLoadingStateKey(type, url)
    const state = await this.store.getItem<LoadingState>(key)
    return state?.isLoading || false
  }

  /**
   * 检查特定项是否正在加载（通过 id）
   */
  async isItemLoading(id: string | number): Promise<boolean> {
    const idStr = id.toString()

    for (const type of Object.values(ResourceType)) {
      const isLoading = await this.getResourceLoadingState(type, idStr)
      if (isLoading) {
        return true
      }
    }

    return false
  }

  async cleanup(_now: number, _maxAge: number) {
    await this.store.clear()
  }
}
