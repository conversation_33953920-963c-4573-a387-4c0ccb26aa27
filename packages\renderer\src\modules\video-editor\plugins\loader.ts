/**
 * 插件加载器
 * 负责在应用启动时自动加载所有插件
 */
import './plugins/material'
import './plugins/sticker'
import './plugins/sound'
import './plugins/text'
import './plugins/music'
import './plugins/script'

/**
 * 初始化所有插件
 * 这个函数应该在应用启动时调用
 */
export function initializePlugins(): void {
  console.log('[PluginLoader] 初始化插件系统')

  // 在这里可以进行一些全局的插件系统初始化操作
  // 例如：
  // - 加载用户配置的插件
  // - 设置插件系统的全局配置
  // - 注册插件系统的全局事件监听器
}

/**
 * 清理所有插件
 * 这个函数应该在应用关闭时调用
 */
export function cleanupPlugins(): void {
  console.log('[PluginLoader] 清理插件系统')

  // 在这里可以进行一些全局的插件系统清理操作
  // 例如：
  // - 保存插件系统的状态
  // - 移除插件系统的全局事件监听器
}
