import React from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { AlertTriangle, RefreshCw } from 'lucide-react'

interface CloudLoadErrorDialogProps {
  open: boolean
  onRetry: () => void
  onClose: () => void
  error?: string
}

/**
 * 云端加载失败错误对话框
 * 当从云端加载编辑器状态失败时显示，提供重试和关闭选项
 */
export const CloudLoadErrorDialog: React.FC<CloudLoadErrorDialogProps> = ({
  open,
  onRetry,
  onClose,
  error
}) => {
  return (
    <Dialog open={open} onOpenChange={() => {}}>
      <DialogContent className="sm:max-w-md [&>button]:hidden">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-destructive/10">
              <AlertTriangle className="h-5 w-5 text-destructive" />
            </div>
            <div>
              <DialogTitle className="text-left">云端数据加载失败</DialogTitle>
              <DialogDescription className="text-left">
                无法从云端加载编辑器状态，请检查网络连接后重试
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>
        
        {error && (
          <div className="mt-4 p-3 bg-muted rounded-md">
            <p className="text-sm text-muted-foreground">错误详情：</p>
            <p className="text-sm font-mono mt-1">{error}</p>
          </div>
        )}
        
        <div className="flex flex-col gap-2 mt-6">
          <Button onClick={onRetry} className="w-full">
            <RefreshCw className="mr-2 h-4 w-4" />
            重试加载
          </Button>
          <Button variant="outline" onClick={onClose} className="w-full">
            关闭编辑器
          </Button>
        </div>
        
        <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-md border border-yellow-200 dark:border-yellow-800">
          <p className="text-sm text-yellow-800 dark:text-yellow-200">
            <strong>注意：</strong>在成功加载云端数据之前，编辑器将保持锁定状态，无法进行任何编辑操作。
          </p>
        </div>
      </DialogContent>
    </Dialog>
  )
}
