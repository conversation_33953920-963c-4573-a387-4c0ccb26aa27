import clsx from 'clsx'
import React from 'react'
import { useDraggable } from '@dnd-kit/core'
import { Overlay } from '@app/rve-shared/types'

/**
 * Props for the TimelineItemHandle component
 */
interface TimelineItemHandleProps {
  /** Position of the handle - either on the left or right side */
  position: 'left' | 'right'
  /** Whether this handle is currently selected */
  isSelected: boolean

  disabled: boolean

  overlay: Overlay
}

/**
 * A draggable handle component used in timeline items.
 * Renders a semi-transparent vertical bar with two lines indicating it's draggable.
 * The handle becomes visible on hover or when selected, and supports both mouse and touch interactions.
 */
export const TimelineItemHandle: React.FC<TimelineItemHandleProps> = ({
  position,
  isSelected,
  disabled,
  overlay
}) => {
  const { setNodeRef, listeners, attributes } = useDraggable({
    id: overlay.id,
    data: {
      type: 'timeline-item-handle',
      overlay: overlay
    }
  })

  return (
    <div
      ref={setNodeRef}
      {...attributes}
      {...listeners}
      className={clsx(
        `bg-gray-400/20 dark:bg-white/10 backdrop-blur-sm w-3 absolute top-0 bottom-0 cursor-ew-resize z-50
          opacity-0
         transition-all duration-200 ease-in-out border-r border-l border-gray-400/30 dark:border-white/20`,
        `${position}-0`,
        !disabled && 'hover:bg-gray-400/30 dark:hover:bg-white/20 group-hover:opacity-100',
        isSelected && 'opacity-100'
      )}
    >
      <div className="absolute inset-0 flex items-center justify-center">
        <div
          className={clsx(
            'space-x-0.5 flex',
            position === 'left' ? 'ml-0' : 'mr-0'
          )}
        >
          <div
            className="w-[1px] h-4 bg-gray-600/90 dark:bg-white/90 rounded-full shadow-glow"
          />
          <div
            className="w-[1px] h-4 bg-gray-600/90 dark:bg-white/90 rounded-full shadow-glow"
          />
        </div>
      </div>
    </div>
  )
}
