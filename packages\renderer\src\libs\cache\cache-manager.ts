import { ImageCacheManager } from './parts/image.cache'
import { ResourceCacheManager } from './parts/resource.cache'
import { KeyFrameCacheManager } from './parts/keyframe.cache'
import { LoadingStateCacheManager } from './parts/loading.cache'
import { StickerLoadingStateCacheManager } from './parts/sticker-loading.cache'
import { EditorStateCacheManager } from './parts/editor.cache.ts'
import { ICacheManager, SubCacheManager } from './types.ts'
import { FontCacheManager } from './parts/font.cache.ts'

/**
 * 基于 IndexedDB + localforage 的缓存管理器
 */
export class CacheManager implements ICacheManager {

  public readonly image: ImageCacheManager
  public readonly font: FontCacheManager
  public readonly resource: ResourceCacheManager
  public readonly keyframe: KeyFrameCacheManager
  public readonly loadingState: LoadingStateCacheManager
  public readonly stickerLoading: StickerLoadingStateCacheManager
  public readonly projectState: EditorStateCacheManager

  private static instance: CacheManager
  private readonly registry: SubCacheManager[] = []

  private constructor() {
    this.image = new ImageCacheManager(this, 'preloaded_images', '预加载图片')
    this.font = new FontCacheManager(this, 'fonts', '字体文件')
    this.resource = new ResourceCacheManager(this, 'resource_cache', '资源')
    this.keyframe = new KeyFrameCacheManager(this, 'video_keyframes', '视频关键帧')
    this.loadingState = new LoadingStateCacheManager(this, 'loading_state', '资源加载状态')
    this.stickerLoading = new StickerLoadingStateCacheManager(this, 'sticker_loading', '贴纸加载状态')
    this.projectState = new EditorStateCacheManager(this)
  }

  public register(subCacheManager: SubCacheManager) {
    this.registry.push(subCacheManager)
  }

  public static createInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager()
    }
    return CacheManager.instance
  }

  /**
   * 清理过期的缓存数据
   */
  async cleanupExpiredCache(maxAge: number = 7 * 24 * 60 * 60 * 1000): Promise<void> {
    const now = Date.now()

    this.registry.forEach(manager => manager.cleanup(now, maxAge))
  }
}

// 导出单例实例
export const cacheManager = CacheManager.createInstance()
