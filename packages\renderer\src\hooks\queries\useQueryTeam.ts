import { QUERY_KEYS } from '@/constants/queryKeys'
import { Member, TeamAPI } from '@/libs/request/api/team'
import { useQuery } from '@tanstack/react-query'
import { PaginationQueryParams } from '../useInfiniteQuery'
import { usePagination } from '../usePagination'
import { PaginationParams } from '@app/shared/types/database.types'

export const useCurrentTeam = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.TEAM_CURRENT],
    queryFn: () => TeamAPI.current({}),
  })
}

export const useTeamMembers = ({ pageNo, pageSize }: PaginationQueryParams = {}) => {
  return useQuery({
    queryKey: [QUERY_KEYS.TEAM_MEMBERS, pageNo, pageSize],
    queryFn: () => TeamAPI.members({ pageNo: pageNo || 1, pageSize: pageSize || 20 }),
  })
}

export const usePaginationTeamMembers = (params: PaginationParams = {}) => {
  return usePagination<Member, PaginationQueryParams>({
    queryKey: [QUERY_KEYS.TEAM_MEMBERS],
    queryFn: TeamAPI.members,
    searchParams: params,
    initialPageSize: 20,
    enabled: true,
  })
}

export const useTeamRoles = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.TEAM_ROLES],
    queryFn: () => TeamAPI.roles({}),
  })
}

export const useTeamList = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.TEAM_LIST],
    queryFn: () => TeamAPI.list({}),
  })
}
