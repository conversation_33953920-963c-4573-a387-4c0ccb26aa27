import { SidebarProvider } from '@/components/ui/sidebar.tsx'
import ReactVideoEditor from '@/modules/video-editor/react-video-editor.tsx'
import React from 'react'
import { useVirtualTab } from '@/contexts'

const VideoEditor: React.FC = () => {
  const { params: { id, projectId } } = useVirtualTab('Editor')

  if (!id || !projectId) return null

  return (
    <SidebarProvider id="SidebarProvider">
      <ReactVideoEditor scriptId={id} projectId={projectId} />
    </SidebarProvider>
  )
}

export default VideoEditor
