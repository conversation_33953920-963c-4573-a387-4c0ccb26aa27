import type { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios'
import errorHandler from '@/libs/tools/ErrorHandler.ts'
import { ENABLE_REQUEST_LOG } from './config'
import { ApiResponse } from '@app/shared/types/database.types.ts'
import { TeamManager, TokenManager } from '@/libs/storage'

// 扩展 Axios 配置类型，添加自定义属性
declare module 'axios' {
  interface InternalAxiosRequestConfig {
    showLoading?: boolean
    showErrorMessage?: boolean
    showSuccessMessage?: boolean
    retryCount?: number
    retryDelay?: number
    _retryCount?: number
  }
}

/**
 * 设置请求拦截器
 * @param instance Axios 实例
 */
export function setupRequestInterceptors(instance: AxiosInstance): void {
  // 请求拦截器
  instance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      // 添加 token 到请求头
      const authHeader = TokenManager.getAuthorizationHeader()
      if (authHeader && config.headers) {
        config.headers['Authorization'] = authHeader
      }

      // 添加 tenant_id 到请求头
      const tenant_id = TeamManager.current()
      if (tenant_id && config.headers) {
        config.headers['tenant-id'] = tenant_id
      }

      // 显示全局加载状态
      if (config.showLoading) {
        // 这里可以实现全局加载状态
        // 例如: showGlobalLoading()
      }

      // 开发环境下打印请求日志
      if (ENABLE_REQUEST_LOG) {
        console.log(`🚀 [API Request] ${config.method?.toUpperCase()} ${config.url}`, config)
      }

      return config
    },
    (error: AxiosError) => {
      return Promise.reject(error)
    },
  )
}

/**
 * 设置响应拦截器
 * @param instance Axios 实例
 */
export function setupResponseInterceptors(instance: AxiosInstance): void {
  // 响应拦截器
  instance.interceptors.response.use(
    (response: AxiosResponse) => {
      const config = response.config
      const data = response.data as ApiResponse

      // 隐藏全局加载状态
      if (config.showLoading) {
        // 这里可以隐藏全局加载状态
        // 例如: hideGlobalLoading()
      }

      // 开发环境下打印响应日志
      if (ENABLE_REQUEST_LOG) {
        console.log(`✅ [API Response] ${config.method?.toUpperCase()} ${config.url}`, data)
      }

      // 处理业务状态码
      if (data && typeof data === 'object' && 'code' in data) {
        // 业务成功
        if (data.code === 0 || data.code === 200) {
          if (config.showSuccessMessage && data.message) {
            // showSuccessMessage(data.message)
          }
          return data.data
        } else {
          // 使用后端返回的 msg 作为错误消息，如果没有则使用预定义的消息
          const errorMessage = data.msg || data.message
          const error = errorHandler.handleBusinessError(data.code, {
            showToast: false,
            customMessage: errorMessage,
          })

          // 特殊状态码处理
          if (data.code === 401) {
            // 未授权，清除token并重定向到登录页
            TokenManager.clearAuthData()
            // 如果当前不在登录页，则重定向到登录页
            if (window.location.pathname !== '/login') {
              window.location.href = '/login'
            }
          }

          return Promise.reject(error)
        }
      }

      // 直接返回响应数据
      return data
    },
    (error: AxiosError) => {
      const config = error.config

      // 隐藏全局加载状态
      if (config?.showLoading) {
        // 这里可以隐藏全局加载状态
        // 例如: hideGlobalLoading()
      }

      if (config?.showErrorMessage !== false) {
        errorHandler.handleHttpError(error)
      }

      return Promise.reject(error)
    },
  )
}

/**
 * 设置请求重试拦截器
 * @param instance Axios 实例
 */
export function setupRetryInterceptor(instance: AxiosInstance): void {
  instance.interceptors.response.use(undefined, async (error: AxiosError) => {
    const config = error.config

    // 如果没有配置重试或已达到最大重试次数，则直接抛出错误
    if (!config || !config.retryCount) {
      return Promise.reject(error)
    }

    // 初始化重试计数
    if (config._retryCount === undefined) {
      config._retryCount = 0
    }

    // 如果已达到最大重试次数，则直接抛出错误
    if (config._retryCount >= config.retryCount) {
      return Promise.reject(error)
    }

    // 增加重试计数
    config._retryCount += 1

    // 创建延迟 Promise
    const delay = config.retryDelay || 1000
    await new Promise(resolve => setTimeout(resolve, delay))

    // 重试请求
    return instance(config)
  })
}
