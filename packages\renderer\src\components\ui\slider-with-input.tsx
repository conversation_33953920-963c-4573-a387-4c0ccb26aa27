import { Slider } from '@/components/ui/slider.tsx'
import { NumberInput } from '@/components/ui/number-input.tsx'
import React from 'react'
import { cn } from '@/components/lib/utils'

export interface SliderWithInput extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'onChange' | 'value'> {
  value: number
  onChange?: (value: number ) => void,
  onChangeCommit?: (value: number ) => void,
  showInput?: boolean,
}

export const SliderWithInput = React.forwardRef<HTMLInputElement, SliderWithInput>(
  function SliderWithInput(props, ref) {
    const {
      className,
      step = 1,
      min = 0,
      max = 100,
      value,
      onChange,
      onChangeCommit,
      showInput = true,
      ...restProps
    } = props

    const inputRef = React.useRef<HTMLInputElement>(null)

    React.useImperativeHandle(ref, () => inputRef.current!)

    // 确保值在有效范围内
    const clampValue = (val: number | null): number => {
      if (val === null || val === undefined) return Number(min)
      const numVal = Number(val)
      if (isNaN(numVal)) return Number(min)
      return Math.max(Number(min), Math.min(Number(max), numVal))
    }

    // 获取当前有效值
    const currentValue = clampValue(value)

    // 处理滑块值变化
    const handleSliderChange = (values: number[]) => {
      const newValue = values[0]
      if (newValue !== undefined && onChange) {
        onChange(clampValue(newValue))
      }
    }

    // 处理滑块值提交（拖拽结束）
    const handleSliderCommit = (values: number[]) => {
      const newValue = values[0]
      if (newValue !== undefined && onChangeCommit) {
        onChangeCommit(clampValue(newValue))
      }
    }

    // 处理数字输入变化
    const handleNumberInputChange = (newValue: number | null) => {
      if (onChange) {
        // 如果输入为null或无效，使用最小值
        if (newValue === null || newValue === undefined) {
          onChange(Number(min))
        } else {
          onChange(clampValue(newValue))
        }
      }
    }

    return (
      <div className={cn('flex flex-1 w-full items-center gap-2', className)} {...restProps}>
        <Slider
          value={[currentValue]}
          onValueChange={handleSliderChange}
          onValueCommit={onChangeCommit ? handleSliderCommit : undefined}
          min={Number(min)}
          max={Number(max)}
          step={Number(step)}
          className="flex-1 w-full"
        />
        {showInput && (
          <NumberInput
            ref={inputRef}
            value={currentValue}
            onChange={handleNumberInputChange}
            min={Number(min)}
            max={Number(max)}
            step={Number(step)}
            className="max-w-20"
          />
        )}
      </div>
    )
  }
)

