import { MaterialPlugin, PluginRegistrationOptions } from './types'

/**
 * 素材插件注册中心
 * 负责管理所有已注册的素材插件
 */
class MaterialPluginRegistry {

  /**
   * 存储所有已注册的插件
   * 键为插件ID，值为插件实例
   */
  private plugins = new Map<string, MaterialPlugin>()

  /**
   * 注册一个素材插件
   * @param plugin 要注册的插件
   * @param options 注册选项
   * @returns 是否注册成功
   */
  public register(plugin: MaterialPlugin, options: PluginRegistrationOptions = {}): boolean {
    const { id } = plugin
    const { override = false } = options

    // 检查插件ID是否已存在
    if (this.plugins.has(id) && !override) {
      console.warn(`[MaterialPluginRegistry] 插件ID "${id}" 已存在，注册失败。如需覆盖，请设置 override: true`)
      return false
    }

    // 注册插件
    this.plugins.set(id, plugin)

    // 调用插件的初始化函数（如果有）
    if (plugin.initialize) {
      try {
        plugin.initialize()
      } catch (error) {
        console.error(`[MaterialPluginRegistry] 插件 "${id}" 初始化失败:`, error)
      }
    }

    console.log(`[MaterialPluginRegistry] 插件 "${id}" 注册成功`)
    return true
  }

  /**
   * 注销一个素材插件
   * @param id 要注销的插件ID
   * @returns 是否注销成功
   */
  public unregister(id: string): boolean {
    const plugin = this.plugins.get(id)
    if (!plugin) {
      console.warn(`[MaterialPluginRegistry] 插件ID "${id}" 不存在，注销失败`)
      return false
    }

    // 调用插件的清理函数（如果有）
    if (plugin.cleanup) {
      try {
        plugin.cleanup()
      } catch (error) {
        console.error(`[MaterialPluginRegistry] 插件 "${id}" 清理失败:`, error)
      }
    }

    // 从注册表中移除插件
    this.plugins.delete(id)
    console.log(`[MaterialPluginRegistry] 插件 "${id}" 注销成功`)
    return true
  }

  /**
   * 获取指定ID的插件
   * @param id 插件ID
   * @returns 插件实例，如果不存在则返回undefined
   */
  public getPlugin(id: string): MaterialPlugin | undefined {
    return this.plugins.get(id)
  }

  /**
   * 获取所有已注册的插件
   * @param sorted 是否按照order排序
   * @returns 插件实例数组
   */
  public getAllPlugins(sorted = true): MaterialPlugin[] {
    const plugins = Array.from(this.plugins.values())

    if (sorted) {
      // 按照order排序，order越小越靠前
      return plugins.sort((a, b) => {
        const orderA = a.order ?? 100
        const orderB = b.order ?? 100
        return orderA - orderB
      })
    }

    return plugins
  }

  /**
   * 检查指定ID的插件是否已注册
   * @param id 插件ID
   * @returns 是否已注册
   */
  public hasPlugin(id: string): boolean {
    return this.plugins.has(id)
  }

  /**
   * 获取已注册的插件数量
   * @returns 插件数量
   */
  public get size(): number {
    return this.plugins.size
  }

  /**
   * 清空所有已注册的插件
   * 会调用每个插件的cleanup函数
   */
  public clear(): void {
    // 获取所有插件ID
    const pluginIds = Array.from(this.plugins.keys())

    // 逐个注销插件
    for (const id of pluginIds) {
      this.unregister(id)
    }

    console.log('[MaterialPluginRegistry] 所有插件已清空')
  }
}

// 创建单例实例
export const materialPluginRegistry = new MaterialPluginRegistry()

// 导出便捷函数

/**
 * 注册一个素材插件
 * @param plugin 要注册的插件
 * @param options 注册选项
 * @returns 是否注册成功
 */
export function registerMaterialPlugin(
  plugin: MaterialPlugin,
  options?: PluginRegistrationOptions
): MaterialPlugin {
  materialPluginRegistry.register(plugin, options)
  return plugin
}

/**
 * 注销一个素材插件
 * @param id 要注销的插件ID
 * @returns 是否注销成功
 */
export function unregisterMaterialPlugin(id: string): boolean {
  return materialPluginRegistry.unregister(id)
}

/**
 * 获取指定ID的插件
 * @param id 插件ID
 * @returns 插件实例，如果不存在则返回undefined
 */
export function getMaterialPlugin(id: string): MaterialPlugin | undefined {
  return materialPluginRegistry.getPlugin(id)
}

/**
 * 获取所有已注册的插件
 * @param sorted 是否按照order排序
 * @returns 插件实例数组
 */
export function getAllMaterialPlugins(sorted = true): MaterialPlugin[] {
  return materialPluginRegistry.getAllPlugins(sorted)
}

/**
 * 检查指定ID的插件是否已注册
 * @param id 插件ID
 * @returns 是否已注册
 */
export function hasMaterialPlugin(id: string): boolean {
  return materialPluginRegistry.hasPlugin(id)
}

/**
 * 获取已注册的插件数量
 * @returns 插件数量
 */
export function getMaterialPluginCount(): number {
  return materialPluginRegistry.size
}

/**
 * 清空所有已注册的插件
 */
export function clearMaterialPlugins(): void {
  materialPluginRegistry.clear()
}
