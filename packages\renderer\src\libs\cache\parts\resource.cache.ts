import localforage from 'localforage'
import { ResourceType } from '@app/shared/types/resource-cache.types.ts'
import { SubCacheManager } from '../types'
import { INDEXEDDB_DATABASE_NAME } from '@/constants/system.ts'

// 缓存数据结构
export interface CachedResource {
  url: string
  localPath: string
  timestamp: number
  version?: string
  resourceType: ResourceType
}

export class ResourceCacheManager extends SubCacheManager {

  // 资源缓存存储
  private readonly resourceCacheStore: LocalForage = localforage.createInstance({
    name: INDEXEDDB_DATABASE_NAME,
    storeName: 'resource_cache',
    description: '资源缓存存储'
  })

  // 内存缓存，用于同步访问
  private memoryCache: Map<string, CachedResource> = new Map()

  /**
   * 生成缓存键
   */
  private getCacheKey(type: ResourceType, url: string): string {
    return `${type}:${url}`
  }

  /**
   * 设置资源缓存
   */
  async setResourceCache(type: ResourceType, url: string, localPath: string, version?: string): Promise<void> {
    const key = this.getCacheKey(type, url)
    const entry: CachedResource = {
      url,
      localPath,
      timestamp: Date.now(),
      version,
      resourceType: type
    }
    await this.resourceCacheStore.setItem(key, entry)

    // 同时更新内存缓存
    this.memoryCache.set(key, entry)
  }

  /**
   * 获取资源缓存
   */
  async getResourceCache(type: ResourceType, url: string): Promise<CachedResource | null> {
    const key = this.getCacheKey(type, url)
    return await this.resourceCacheStore.getItem<CachedResource>(key)
  }

  /**
   * 检查资源是否已缓存
   */
  async isResourceCached(type: ResourceType, url: string): Promise<boolean> {
    const entry = await this.getResourceCache(type, url)
    return entry !== null
  }

  /**
   * 获取资源的本地路径（同步方式，用于兼容现有代码）
   */
  getResourcePathSync(type: ResourceType, url: string): string | null {
    const key = this.getCacheKey(type, url)
    const entry = this.memoryCache.get(key)
    return entry?.localPath || null
  }

  /**
   * 获取资源的本地路径（异步版本）
   */
  async getResourcePath(type: ResourceType, url: string): Promise<string | null> {
    const entry = await this.getResourceCache(type, url)
    return entry?.localPath || null
  }

  /**
   * 获取特定类型的所有资源缓存
   */
  async getResourceCacheByType(type: ResourceType): Promise<Record<string, string>> {
    const result: Record<string, string> = {}

    await this.resourceCacheStore.iterate((entry: CachedResource) => {
      if (entry.resourceType === type) {
        result[entry.url] = entry.localPath
      }
    })

    return result
  }

  /**
   * 清除资源缓存
   */
  async clearResourceCache(type?: ResourceType, url?: string): Promise<void> {
    if (type && url) {
      // 清除特定资源
      const key = this.getCacheKey(type, url)
      await this.resourceCacheStore.removeItem(key)
    } else if (type) {
      // 清除特定类型的所有资源
      const keysToRemove: string[] = []

      await this.resourceCacheStore.iterate((entry: CachedResource) => {
        if (entry.resourceType === type) {
          keysToRemove.push(this.getCacheKey(type, entry.url))
        }
      })

      for (const key of keysToRemove) {
        await this.resourceCacheStore.removeItem(key)
      }
    } else {
      // 清除所有资源
      await this.resourceCacheStore.clear()
    }
  }

  override init() {
    void this.resourceCacheStore
      .iterate((entry: CachedResource, key: string) => {
        this.memoryCache.set(key, entry)
      })
  }

  override async cleanup(now: number, maxAge: number) {
    const keysToRemove: string[] = []
    // 清理资源缓存
    await this.resourceCacheStore.iterate((entry: CachedResource, key: string) => {
      if (now - entry.timestamp > maxAge) {
        keysToRemove.push(key)
      }
    })

    for (const key of keysToRemove) {
      await this.resourceCacheStore.removeItem(key)
    }
  }
}
