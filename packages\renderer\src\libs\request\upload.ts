import { UploadOptions, UploadOptionsForIPC } from '@app/shared/types/ipc/file-uploader'

export enum UploadModule {
  media = 'media',
  publishing = 'publishing',
  script = 'script',
}

/**
 * 将UploadOptions转换为UploadOptionsForIPC（移除回调函数）
 */
function sanitizeOptionsForIPC(options?: UploadOptions): UploadOptionsForIPC {
  if (!options) return {}

  const { onProgress, ...ipcOptions } = options
  return ipcOptions
}

export async function uploadFileToOss(file: File, folderUuid: string, fileMd5?: string) {
  try {
    // 转换为ArrayBuffer并使用IPC上传
    const arrayBuffer = await file.arrayBuffer()
    const result = await uploadBufferViaIPC(arrayBuffer, file.name, folderUuid, fileMd5, UploadModule.media, {
      dirPrefix: 'user-uploads/',
    })

    if (result.success) {
      return result.url
    } else {
      throw new Error(result.error || '上传失败')
    }
  } catch (error) {
    console.error('uploadFileToOss 失败:', error)
    throw error
  }
}

/**
 * 使用IPC上传Buffer到OSS（通过主进程）
 */
export async function uploadBufferViaIPC(
  buffer: ArrayBuffer,
  fileName: string,
  folderUuid: string,
  fileMd5?: string,
  module?: UploadModule,
  options?: UploadOptions,
) {
  try {
    // 将ArrayBuffer转换为普通数字数组，这样可以被IPC序列化
    const uint8Array = new Uint8Array(buffer)
    const byteArray = Array.from(uint8Array)
    console.log({ byteArray: byteArray.slice(0, 10), fileName }) // 只打印前10个字节用于调试

    // 生成唯一的上传ID
    const uploadId = Math.random().toString(36).substring(2, 15)

    // 如果有进度回调，设置 IPC 事件监听器
    if (options?.onProgress) {
      const progressHandler = (_event: any, data: { uploadId: string; progress: number }) => {
        if (data.uploadId === uploadId) {
          options.onProgress!(data.progress)
        }
      }

      window.electronAPI.ipcRenderer.on('upload-progress', progressHandler)

      const cleanup = () => {
        window.electronAPI.ipcRenderer.removeListener('upload-progress', progressHandler)
      }

      setTimeout(cleanup, 300000) // 5分钟后清理
    }

    const result = await window.fileUploader.uploadBufferToOSS({
      buffer: byteArray,
      fileName,
      uploadId,
      folderUuid,
      fileMd5,
      module: module || UploadModule.media,
      options: sanitizeOptionsForIPC(options),
    })

    if (!result.success) {
      throw new Error(result.error || '上传失败')
    }

    return result
  } catch (error) {
    console.error('IPC上传Buffer失败:', error)
    throw error
  }
}
