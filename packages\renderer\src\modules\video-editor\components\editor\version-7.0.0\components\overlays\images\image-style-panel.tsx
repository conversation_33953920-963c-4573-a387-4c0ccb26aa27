import React from 'react'
import { StickerOverlay } from '@app/rve-shared/types'
import { MediaFilterPresetSelector } from '../common/media-filter-preset-selector'
import { MediaPaddingControls } from '../common/media-padding-controls'
import { useOverlayEditing } from '@rve/editor/contexts'
import { FilterHelper } from '../../../utils/filter-helper'
import { ColorSlider } from '@/components/ui/color-slider'

export const ImageStylePanel = () => {
  const { localOverlay: stickerOverlay, requestUpdate: updateOverlay } = useOverlayEditing<StickerOverlay>()

  const currentFilter = stickerOverlay?.styles?.filter || ''

  const getFilterValue = (filterName: string, defaultValue: number = 100) => {
    return FilterHelper.extractFilterValue(currentFilter, filterName, defaultValue)
  }

  const updateFilterValue = (filterName: string, value: number, unit: string = '%', commit?: boolean) => {
    const newFilter = FilterHelper.updateFilter(currentFilter, filterName, value, unit)
    updateOverlay({ styles: { filter: newFilter } }, commit)
  }

  return (
    <div className="space-y-6">
      <div className="space-y-4 rounded-md  p-4 border border-border">
        <h3 className="text-sm font-medium text-foreground">Appearance</h3>

        <MediaFilterPresetSelector
          localOverlay={stickerOverlay}
          handleStyleChange={updateOverlay}
        />

        {/* Border Radius */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-xs text-muted-foreground">
              Border Radius
            </label>
            <span className="text-xs text-muted-foreground min-w-[40px] text-right">
              {stickerOverlay?.styles?.borderRadius ?? '0px'}
            </span>
          </div>
          <input
            type="number"
            value={parseInt(stickerOverlay?.styles?.borderRadius ?? '0')}
            onChange={e =>
              updateOverlay({ styles: { borderRadius: `${e.target.value}px` } }, true)}
            min="0"
            className="w-full bg-background border border-input rounded-md text-xs p-2 hover:border-accent-foreground transition-colors"
          />
        </div>

        {/* Brightness Control */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-xs text-muted-foreground">Brightness</label>
            <span className="text-xs text-muted-foreground min-w-[40px] text-right">
              <span>{getFilterValue('brightness', 100)}%</span>
            </span>
          </div>
          <div className="flex items-center gap-3">
            <ColorSlider
              value={getFilterValue('brightness', 100)}
              onChange={(value, commit) => updateFilterValue('brightness', value, '%', commit)}
              trackClassName="bg-gradient-to-r from-black via-gray-500 to-white"
              max={200}
              min={0}
            />
          </div>
        </div>

        {/* Media Padding Controls */}
        <MediaPaddingControls
          localOverlay={stickerOverlay}
          handleStyleChange={updateOverlay}
        />
      </div>
    </div>
  )
}
