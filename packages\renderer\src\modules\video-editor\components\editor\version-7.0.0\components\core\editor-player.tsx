import React from 'react'
import { VideoPlayer } from '@rve/editor/components/core/video-player.tsx'
import {
  PlayerControls
} from '@rve/editor/components/core/player-controls.tsx'

export const EditorPlayer: React.FC = () => {
  return (
    <div
      className="size-full flex flex-col overflow-hidden"
      style={{
        maxHeight: '-webkit-fill-available' /* Safari fix */,
      }}
    >
      <div className="size-full flex-grow flex flex-col lg:flex-row overflow-hidden">
        <VideoPlayer />
      </div>

      <PlayerControls />
    </div>
  )
}
