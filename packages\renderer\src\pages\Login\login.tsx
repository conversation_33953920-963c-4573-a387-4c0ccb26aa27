import React, { useEffect, useState } from 'react'
import './login.css'
import { MessageSquareMore, Smartphone } from 'lucide-react'
import { AuthModule } from '@/libs/request/api/auth'
import { TokenManager } from '@/libs/storage'
import { useNavigate } from 'react-router'
import { z } from 'zod'
import { refreshTeam } from '@/libs/request/team'

const mobileSchema = z
  .string()
  .min(11, '手机号太短')
  .max(11, '手机号太长')
  .regex(/^1[3-9]\d{9}$/, '请输入正确的手机号格式')
const codeSchema = z.string().min(4, '请输入验证码').max(6, '验证码长度不正确').regex(/^\d+$/, '验证码只能包含数字')

const safe = <Fn extends (...args: any[]) => any>(fn: Fn) => {
  return (...args: Parameters<Fn>): ReturnType<Fn> | void => {
    try {
      return fn(...args)
    } catch (error) {
      console.error(error)
    }
  }
}

export default function Login() {
  const navigate = useNavigate()
  const [wait, setWait] = useState(0)
  const [code, setCode] = useState('')
  const [codeInit, setCodeInit] = useState(false)
  const [codeError, setCodeError] = useState('')
  const [mobile, setMobile] = useState('')
  const [mobileInit, setMobileInit] = useState(false)
  const [mobileError, setMobileError] = useState('')
  const [error, setError] = useState('')

  const updateWait = () => {
    const iter = (v: number) => {
      setWait(v)
      if (!v) return
      setTimeout(() => iter(v - 1), 1000)
    }
    iter(60)
  }

  const revalidMobile = (value?: string) => {
    setMobileInit(true)
    const result = mobileSchema.safeParse(value ?? mobile)
    if (result.success) {
      setMobileError('')
      return true
    }
    setMobileError(result.error.errors[0].message)
  }

  const revalidCode = (value?: string) => {
    setCodeInit(true)
    const result = codeSchema.safeParse(value ?? code)
    if (result.success) {
      setCodeError('')
      return true
    }
    setCodeError(result.error.errors[0].message)
  }

  const sendCode = safe(async () => {
    if (!revalidMobile()) return
    try {
      await AuthModule.sendSMSCode({ mobile, scene: 1 })
      updateWait()
    } catch (error: any) {
      setError(error?.message || '发送验证码失败')
    }
  })

  const refresh = async () => {
    if (!TokenManager.isLoggedIn()) return
    const group = await refreshTeam()
    if (group) navigate('/home')
    if (!group) navigate('/login/group')
  }

  const login = safe(async () => {
    if (!revalidCode() || !revalidMobile()) return
    try {
      TokenManager.saveLoginData(await AuthModule.codeLogin({ mobile, code: Number(code) }))
      refresh()
    } catch (error: any) {
      setError(error?.message || '登录失败')
    }
  })

  useEffect(() => {
    refresh()
  }, [])

  return (
    <div className="absolute inset-0 flex flex-col items-stretch justify-center z-10 px-[6.25vw]">
      <div className="text-[2vw]">欢迎登录</div>
      <div className="flex flex-col mt-[3.33vw] gap-[2vw] md:gap-[1.75vw] xl:gap-[1.25vw]">
        <div className="flex-1 flex relative">
          <input
            className="input flex-1 pl-[2.71vw] h-[3vw] text-[11px] md:text-sm lg:text-base"
            placeholder="请输入手机号"
            value={mobile}
            onChange={e => {
              setMobile(e.currentTarget.value)
              if (mobileInit) revalidMobile(e.currentTarget.value)
              else setMobileError('')
            }}
            onBlur={() => revalidMobile()}
          />
          <Smartphone className="absolute left-[.833vw] top-1/2 -translate-y-1/2 size-3 md:size-4 lg:size-5 xl:size-6" />
          {mobileError && (
            <p className="absolute left-[1vw] bottom-0 translate-y-1/1 text-destructive text-[10px] lg:text-xs xl:text-sm">
              {mobileError}
            </p>
          )}
        </div>
        <div className="flex-1 flex relative">
          <input
            className="input flex-1 pl-[2.71vw] h-[3vw] text-[11px] md:text-sm lg:text-base"
            placeholder="请输入验证码"
            value={code}
            onChange={e => {
              setCodeError('')
              if (!/\d*/.test(e.currentTarget.value)) return
              setCode(e.currentTarget.value)
              if (codeInit) revalidCode(e.currentTarget.value)
              else setCodeError('')
            }}
            onBlur={() => revalidCode()}
          />
          <MessageSquareMore className="absolute left-[.833vw] top-1/2 -translate-y-1/2 size-3 md:size-4 lg:size-5 xl:size-6" />
          {codeError && (
            <p className="absolute left-[1vw] bottom-0 translate-y-1/1 text-sm text-destructive text-[10px] lg:text-xs xl:text-sm">
              {codeError}
            </p>
          )}
          <button
            className="absolute right-[1.25vw] top-1/2 -translate-y-1/2 text-[11px] md:text-sm lg:text-base
                  text-blue-500 hover:text-blue-500/90 cursor-pointer disabled:text-blue-700"
            onClick={sendCode}
            disabled={!!wait}
          >
            {wait ? `${wait}s` : '获取验证码'}
          </button>
        </div>
      </div>
      <div className="mt-[6.4vw] flex relative">
        <div
          className="[background:linear-gradient(91.55deg,rgba(0,246,254,0.6)1.6%,rgba(255,106,0,0.6)99.15%)]
                absolute -inset-3 rounded-full blur-xs"
        />
        <button className="flex-1 h-[3.33vw] rounded-full bg-black z-10 cursor-pointer text-[.833vw]" onClick={login}>
          登录
        </button>
        {error && <p className="absolute left-5 bottom-0 translate-y-2/1 text-sm text-destructive">{error}</p>}
      </div>
    </div>
  )
}
