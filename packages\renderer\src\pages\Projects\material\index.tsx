import React, { useState, useMemo, useEffect } from 'react'
import { useParams } from 'react-router'
import { Edit, Trash, FolderPlus, FolderInput, RefreshCw } from 'lucide-react'
import { useQueryMaterialDirectoryList, useQueryMediaList } from '@/hooks/queries/useQueryMaterial'
import { ResourceModule } from '@/libs/request/api/resource'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { MaterialResource, ResourceSource } from '@/types/resources'
import { cn } from '@/components/lib/utils'
import TreeList, { TreeNode, getPathChain, isValidFolderId } from '@/components/TreeList'
import { Button } from '@/components/ui/button'
import { useDeleteModal } from '@/components/modal/delete'
import MoveDialog from './components/MoveDialog'
import MediaTypeSelector from './components/MediaTypeSelector'
import MaterialFilterBar from './components/MaterialFilterBar'
import MediaItem, { MediaAction, FolderAction } from './components/MediaItem'
import UploaderCard from './components/MaterialUpload'
import { SearchInput } from '@/components/ui/search-input'
import { useItemActions } from '@/hooks/useItemActions'

const Material: React.FC = () => {
  const params = useParams()
  const queryClient = useQueryClient()
  const [activeTab, setActiveTab] = useState(0)
  const [orientation, setOrientation] = useState(MaterialResource.MediaStyle.HORIZONTAL)
  const { data: treeData, isSuccess: isTreeSuccess } = useQueryMaterialDirectoryList(
    {
      projectId: Number(params.projectId),
    },
    { enabled: !!params.projectId },
  )
  const [moveDialogOpen, setMoveDialogOpen] = useState(false)
  const [currentFolderId, setCurrentFolderId] = useState(treeData?.[0]?.id ?? '')
  const [moveId, setMoveId] = useState('')
  const [moveType, setMoveType] = useState(ResourceSource.FOLDER)
  const [selectedMediaItems, setSelectedMediaItems] = useState<Set<string>>(new Set())
  const [selectedFolderItems, setSelectedFolderItems] = useState<Set<string>>(new Set())
  const [filters, setFilters] = useState<MaterialResource.MaterialMediaParams>({
    projectId: Number(params.projectId),
    folderUuid: '',
    sortField: MaterialResource.SortField.UPLOAD_TIME,
    sortOrder: MaterialResource.SortOrder.ASC,
    createAtRange: [],
    durationRange: [],
    useCountRange: undefined, // 合成次数
    quoteCountRange: undefined, // 引用次数
    keyword: undefined,
    resType: undefined,
  })
  const { createItem, renameItem, deleteItem } = useItemActions()

  const deleteModal = useDeleteModal()

  const { data: mediaList, isLoading } = useQueryMediaList(
    filters,
    isTreeSuccess && currentFolderId !== '',
  )

  const childFolders = useMemo(() => {
    if (!treeData) return []

    const findChildren = (nodes: any[]): any[] => {
      for (const node of nodes) {
        if (String(node.id) === String(currentFolderId)) {
          return node.children || []
        }

        if (node.children && node.children.length > 0) {
          const result = findChildren(node.children)
          if (result.length > 0) {
            return result
          }
        }
      }
      return []
    }
    let children = findChildren(treeData)

    if (filters.keyword && filters.keyword !== '') {
      const keyword = filters.keyword.toLowerCase()
      children = children.filter(child => child.label?.toLowerCase().includes(keyword))
    }

    return children
  }, [treeData, currentFolderId, filters.keyword])

  const folderAsMediaItems: MaterialResource.Media[] = childFolders.map(folder => ({
    fileId: folder.id,
    fileName: folder.label,
    folderUuid: folder.raw.parentId,
    childrenFolder: folder.children.length || 0,
    mediaNum: folder.raw.imageCount + folder.raw.videoCount,
    resType: 0, // 代表文件夹
    createTime: folder.raw.createdAt || new Date().toISOString(),
  }))

  useEffect(() => {
    setFilters(prevFilters => ({
      ...prevFilters,
      folderUuid: currentFolderId,
    }))
  }, [currentFolderId])

  useEffect(() => {
    if (!isTreeSuccess || !treeData || treeData.length === 0) return

    const firstFolderId = treeData[0].id

    // 当前无选中目录 或 选中的目录在 treeData 中已不存在
    if (!currentFolderId || !isValidFolderId(treeData, currentFolderId)) {
      setCurrentFolderId(firstFolderId)
      handleFolderClick(firstFolderId)
    }
  }, [isTreeSuccess, treeData, currentFolderId])

  const onRefresh = () => {
    queryClient.invalidateQueries({
      queryKey: [QUERY_KEYS.MATERIAL_MEDIA_LIST],
    })
  }

  const toggleSelect = (fileId: string, isFolder: boolean) => {
    if (isFolder) {
      setSelectedFolderItems(prev => {
        const next = new Set(prev)
        if (next.has(fileId)) {
          next.delete(fileId)
        } else {
          next.add(fileId)
        }
        return next
      })
    } else {
      setSelectedMediaItems(prev => {
        const next = new Set(prev)
        if (next.has(fileId)) {
          next.delete(fileId)
        } else {
          next.add(fileId)
        }
        return next
      })
    }
  }

  const toggleSelectAll = () => {
    if (allSelected) {
      setSelectedMediaItems(new Set())
      setSelectedFolderItems(new Set())
    } else {
      const allMediaIds =
        mediaList?.pages.flatMap(page => page.list.map(media => media.fileId)) || []
      const allFolderIds = folderAsMediaItems.map(folder => folder.fileId)
      setSelectedMediaItems(new Set(allMediaIds))
      setSelectedFolderItems(new Set(allFolderIds))
    }
  }

  const allSelected = useMemo(() => {
    const allMediaIds = mediaList?.pages.flatMap(page => page.list.map(media => media.fileId)) || []
    const allFolderIds = folderAsMediaItems.map(folder => folder.fileId)
    const totalCount = allMediaIds.length + allFolderIds.length

    if (totalCount === 0) return false

    return (
      allMediaIds.every(id => selectedMediaItems.has(id)) &&
      allFolderIds.every(id => selectedFolderItems.has(id))
    )
  }, [mediaList, folderAsMediaItems, selectedMediaItems, selectedFolderItems])

  const selectedCount = selectedMediaItems.size + selectedFolderItems.size

  const handleMove = async (targetFolderId: string) => {
    // 移动媒体文件
    if (selectedMediaItems.size > 0) {
      await ResourceModule.media.move({
        fileIds: Array.from(selectedMediaItems),
        folderUuid: targetFolderId,
      })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_MEDIA_LIST] })
    }

    // 移动文件夹
    if (selectedFolderItems.size > 0) {
      await ResourceModule.directory.move({
        folderIds: Array.from(selectedFolderItems),
        parentId: targetFolderId,
      })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_DIRECTORY_LIST] })
    }

    setSelectedMediaItems(new Set())
    setSelectedFolderItems(new Set())
  }

  const handleBatchDelete = async (isPermanent: boolean) => {
    if (selectedMediaItems.size > 0) {
      await ResourceModule.media[isPermanent ? 'delete' : 'recycle']({
        fileIds: [...selectedMediaItems],
      })
    }
    if (selectedFolderItems.size > 0) {
      await ResourceModule.directory[isPermanent ? 'delete' : 'recycle']({
        folderIds: [...selectedFolderItems],
      })
    }
    queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_MEDIA_LIST] })
    queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_DIRECTORY_LIST] })
    setSelectedMediaItems(new Set())
    setSelectedFolderItems(new Set())
  }
  const folderActions: FolderAction[] = [
    {
      icon: <FolderPlus className="w-4 h-4" />,
      label: '新建文件夹',
      onClick: (nodeId: string, _parentId: string, _label: string) => {
        createItem(ResourceSource.FOLDER, nodeId, {
          label: '文件夹名称',
          headerTitle: '文件夹',
        })
      },
    },
    {
      icon: <FolderInput className="w-4 h-4" />,
      label: '移动',
      onClick: (nodeId: string, _parentId: string, _label: string) => {
        setMoveType(ResourceSource.FOLDER)
        setMoveId(nodeId)
        setMoveDialogOpen(true)
      },
    },
    {
      icon: <Edit className="w-4 h-4" />,
      label: '重命名',
      onClick: (nodeId: string, _parentId: string, label: string) => {
        renameItem(ResourceSource.FOLDER, nodeId, label, {
          label: '文件夹名称',
          headerTitle: '文件夹',
        })
      },
    },
    {
      icon: <Trash className="w-4 h-4" />,
      label: '删除',
      onClick: (nodeId: string, _parentId: string, label: string) => {
        deleteItem(ResourceSource.FOLDER, nodeId, label)
      },
    },
  ]
  const mediaActions: MediaAction[] = [
    {
      icon: <Edit className="w-4 h-4" />,
      label: '修改',
      onClick: (fileId, fileName, _folderUuid) => {
        renameItem(ResourceSource.MEDIA, fileId, fileName, {
          label: '素材名称',
          headerTitle: '素材',
        })
      },
    },
    {
      icon: <FolderInput className="w-4 h-4" />,
      label: '移动到',
      onClick: (fileId, _fileName, _folderUuid) => {
        setMoveType(ResourceSource.MEDIA)
        setMoveId(fileId)
        setMoveDialogOpen(true)
      },
    },
    {
      icon: <Trash className="w-4 h-4" />,
      label: '删除',
      onClick: (fileId, fileName, _folderUuid) => {
        deleteItem(ResourceSource.MEDIA, fileId, fileName)
      },
    },
  ]
  const mediaCount = useMemo(() => {
    const mediaItemsCount =
      mediaList?.pages?.reduce((total, page) => {
        return total + (page.list?.length || 0)
      }, 0) ?? 0
    const folderCount = folderAsMediaItems.length
    return mediaItemsCount + folderCount
  }, [mediaList, folderAsMediaItems])

  const handleMoveConfirm = async (selectedNode: TreeNode) => {
    if (moveType === ResourceSource.MULTI_SELECT) {
      handleMove(selectedNode.id)
    } else {
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_MEDIA_LIST] })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_DIRECTORY_LIST] })
    }
  }

  const folderPath = useMemo(() => {
    if (!currentFolderId || !treeData) return []
    return getPathChain(treeData, currentFolderId) ?? []
  }, [currentFolderId, treeData])

  const handleFolderClick = (folderId: string) => {
    setCurrentFolderId(folderId)
    queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_MEDIA_LIST] })
  }

  return (
    <div className="p-4 flex flex-col flex-1 h-full w-full overflow-auto">
      <SearchInput
        placeholder="请输入关键词搜索"
        value={filters.keyword}
        onChange={e => setFilters(prev => ({ ...prev, keyword: e.target.value }))}
        containerClassName="absolute right-4 top-4"
      />
      <MediaTypeSelector
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        setFilters={setFilters}
        onCreateFolder={() => createItem(ResourceSource.FOLDER, currentFolderId, {
          label: '文件夹名称',
          headerTitle: '文件夹',
        })}
      />
      <MaterialFilterBar
        filters={filters}
        setFilters={setFilters}
        orientation={orientation}
        setOrientation={setOrientation}
      />
      <div className="border-t mt-2 mb-3" />
      <div className="flex flex-1 overflow-auto">
        <TreeList
          data={treeData}
          defaultExpandAll={true}
          className="w-64 flex-shrink-0 overflow-auto border-r"
          actions={folderActions}
          showEllipsis={true}
          selectedId={currentFolderId}
          onSelect={node => {
            handleFolderClick(node.id)
          }}
        />
        <div className="flex-1 overflow-auto">
          <div className="flex items-center justify-between text-sm text-gray-600 pl-4">
            <div className="flex items-center space-x-1">
              {folderPath.map((folder, index) => (
                <React.Fragment key={folder.id}>
                  <button
                    className={cn('hover:underline', {
                      'text-primary-highlight1': folder.id === currentFolderId,
                    })}
                    onClick={() => handleFolderClick(folder.id)}
                  >
                    {folder.label}
                  </button>
                  {index < folderPath.length - 1 && <span>{'>'}</span>}
                </React.Fragment>
              ))}
            </div>
            <div className="flex justify-end items-center space-x-4">
              {selectedCount !== 0 && (
                <div>
                  <Button
                    variant="default"
                    size="sm"
                    className="bg-primary-highlight1 hover:bg-blue-400 text-white"
                    onClick={() => {
                      setMoveType(ResourceSource.MULTI_SELECT)
                      setMoveDialogOpen(true)
                    }}
                  >
                    移动到
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    className="bg-primary-highlight1 hover:bg-blue-400 text-white ml-2"
                    onClick={() => {
                      deleteModal({
                        kind: '',
                        name: '所选文件',
                        danger: true,
                        customButtons: [
                          {
                            label: '放入回收站',
                            onClick: async () => {
                              await handleBatchDelete(false)
                            },
                          },
                          {
                            label: '彻底删除',
                            onClick: async () => {
                              await handleBatchDelete(true)
                            },
                          },
                        ],
                      })
                    }}
                  >
                    删除
                  </Button>
                </div>
              )}

              <span>素材总数：{mediaCount}</span>
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={allSelected}
                  onChange={toggleSelectAll}
                  className="mr-1 accent-primary-highlight1"
                />
                全选
              </label>
              <span> | </span>
              <span>已选 {selectedCount}</span>
              <div className="flex items-center">
                <RefreshCw className="inline-block w-4 h-4 mr-1 text-primary-highlight1" />
                <button className="hover:underline" onClick={onRefresh}>
                  刷新
                </button>
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center h-full">加载中...</div>
          ) : (
            <div className="flex flex-wrap gap-4 p-4 overflow-auto">
              <UploaderCard currentFolderId={currentFolderId} orientation={orientation} />
              {folderAsMediaItems.map(folder => (
                <MediaItem
                  key={folder.fileId}
                  orientation={orientation}
                  media={folder}
                  isSelected={selectedFolderItems.has(folder.fileId)}
                  isFolder={true}
                  actions={folderActions}
                  onToggleSelect={fileId => toggleSelect(fileId, true)}
                />
              ))}

              {mediaList?.pages.map(page =>
                page.list.map(media => (
                  <MediaItem
                    key={media.fileId}
                    orientation={orientation}
                    media={media}
                    isSelected={selectedMediaItems.has(media.fileId)}
                    isFolder={false}
                    actions={mediaActions}
                    onToggleSelect={fileId => toggleSelect(fileId, false)}
                  />
                )),
              )}
            </div>
          )}
        </div>
      </div>
      <MoveDialog
        open={moveDialogOpen}
        moveId={moveId}
        moveType={moveType}
        onOpenChange={setMoveDialogOpen}
        onConfirm={handleMoveConfirm}
      />
    </div>
  )
}

export default Material
