import React from 'react'
import { IndexableTrack } from '@app/rve-shared/types'
import { ContextMenu, ContextMenuContent, ContextMenuItem, ContextMenuTrigger } from '@/components/ui/context-menu.tsx'
import { ClipboardPaste } from 'lucide-react'
import { useTimeline } from '@rve/editor/contexts'
import { isOverlayAcceptableByTrack } from '@rve/editor/utils/track-helper.ts'

type TimelineTrackContextMenuProps = React.PropsWithChildren<{
  track: IndexableTrack
}>

export const TimelineTrackContextMenu: React.FC<TimelineTrackContextMenuProps> = ({ children, track }) => {
  const { mouseOnCurrentFrame, clipboard: { clipped, pasteOverlay }, setIsContextMenuOpen } = useTimeline()

  return (
    <ContextMenu onOpenChange={setIsContextMenuOpen}>
      <ContextMenuTrigger asChild>{children}</ContextMenuTrigger>
      <ContextMenuContent className="dark:bg-slate-900 dark:border-slate-800">
        <ContextMenuItem
          disabled={!clipped || !isOverlayAcceptableByTrack(clipped, track)}
          className="dark:hover:bg-slate-800 dark:focus:bg-slate-800 dark:text-slate-200"
          onClick={e => {
            e.stopPropagation()
            if (mouseOnCurrentFrame !== null) {
              pasteOverlay(track.index, mouseOnCurrentFrame)
            }
          }}
        >
          <ClipboardPaste className="mr-4 h-4 w-4" />
          粘贴
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  )
}
