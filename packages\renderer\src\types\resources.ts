import { PaginationParams } from '@app/shared/types/database.types.ts'

export type CommonCategory = {
  id: string
  name: string
  num?: number
  sort?: number
}

export enum CoverType {
  PHOTO = 'photo',
}

export interface BaseResourceQueryParams extends PaginationParams {
  categoryIds?: string[]
  search?: string
}

export interface BaseResource {
  id: number
  authorName: string
  title: string
  cover: Cover | null
  contentType: string
  createTime: number
  tags: string[]
  top: number
  featured: number
  auditState: number
  subCategoryId: any
  version: string
}

export interface InteractInfo {
  collected: boolean
  collectedCount: any
  refCount: any
}

export interface Cover {
  type: CoverType
  url: string
}

//上传本地资源：我的贴纸/我的音效/我的音乐
export interface UploadLocal {
  folderUuid: string
  title: string
  fileMd5: string
  contentType: string
  objectId: string
}

export enum ResourceSource {
  MEDIA = 'media',
  FOLDER = 'folder',
  MULTI_SELECT = 'multi_select',
  LOCAL_STICK = 'local_stick',
  LOCAL_MUSIC = 'local_music',
  LOCAL_SOUND = 'local_sound',
  LOCAL_STICK_FOLDER = 'local_stick_folder',
  LOCAL_MUSIC_FOLDER = 'local_music_folder',
  LOCAL_SOUND_FOLDER = 'local_sound_folder',
}

export namespace PasterResource {
  export interface Paster extends BaseResource {
    content: PasterContent
    interactInfo: InteractInfo
  }

  export interface PasterContent {
    fileUrl: string // 完整资源URL，用于编辑器中实际使用
    thumbUrl: string // 动态缩略图URL，用于悬停预览
    width: number
    height: number
  }

  // 三层加载状态枚举
  export enum LoadingLayer {
    COVER = 'cover', // 第一层：cover.url 静态图片
    THUMB = 'thumb', // 第二层：thumbUrl 动态缩略图
    FILE = 'file', // 第三层：fileUrl 完整资源
  }

  // 贴纸加载状态接口
  export interface StickerLoadingState {
    coverId: string | number // 贴纸ID
    coverLoaded: boolean // cover.url 是否已加载
    thumbLoaded: boolean // thumbUrl 是否已加载
    thumbLoading: boolean // thumbUrl 是否正在加载
    fileLoaded: boolean // fileUrl 是否已缓存
    fileLoading: boolean // fileUrl 是否正在下载
    currentLayer: LoadingLayer // 当前显示的层级
  }

  // 创建本地资源
  export interface PasterLocal extends Paster{
    fileId: string
    fileName: string
    folderUuid: string
  }
}

export namespace SoundResource {
  export interface Sound extends BaseResource {
    content: SoundContent
    interactInfo: InteractInfo
  }

  export interface SoundContent {
    itemUrl: string
    durationMsec: number
  }
}

export namespace FontResource {
  export interface BubbleLetters extends BaseResource {
    content: BubbleContent
    interactInfo: InteractInfo
  }

  export interface BubbleContent {
    name: string
    coverUrl: string
    itemUrl?: string
    zipUrl?: string
    zipV3Url?: string
    content?: string
  }

  export interface Font extends BaseResource {
    content: FontContent
    interactInfo: InteractInfo
  }

  export interface FontContent {
    name: string
    url: string
    textColor: string
    borderColor: any
    borderWidth: number
  }
}

export namespace FontStyleResource {
  export interface FontStyle extends BaseResource {
    content: FontStlyeContent
    interactInfo: InteractInfo
  }

  export interface FontStlyeContent {
    fontName: string
    fontPath: string
    textColor: string
    borderColor: string
    borderWidth: number
    bold: boolean
    flag: number
    italic: boolean
    itemUrl: string
    alignment: number
    cateLevel: number
    fontSize: number
    presetId: number
    underline: boolean
    textAlpha: number
    lineSpacing: number
    shadowBlur: number
    textureUrl: string
    jyEffectId: string
    shadowAngle: number
    shadowColor: string
    letterSpacing: number
    jyResourceId: string
    shadowDistance: number
    underlineWidth: number
    backgroundAlpha: number
    backgroundColor: string
    underlineOffset: number
    flowerZipV3Url: string
    shadowColorAlpha: number
  }
}
// 素材
export namespace MaterialResource {
  //素材文件夹
  export interface MaterialDirectoryParams {
    projectId: number | undefined
    keyword?: string
  }

  export interface Directory {
    folderId: string
    folderName: string
    parentId: string | undefined
    icons: string | null
    folderCount: number
    videoCount: number
    audioCount: number
    imageCount: number
    fileSize: number | null
    createdAt: number
    updatedAt: number | null
    //本地
    resourcesCount
  }
  export interface LocalDirectory extends Directory {
    resourcesCount: number
  }

  //素材媒体资源文件
  export enum MediaType {
    VIDEO = 1,
    AUDIO = 2,
    IMAGE = 3,
  }

  export enum SortField {
    UPLOAD_TIME = 'uploadTime',
    FILE_SIZE = 'fileSize',
    DURATION = 'duration',
    FILE_NAME = 'fileName',
    QUOTE_COUNT = 'quoteCount',
  }

  export const SortFieldLabelMap: Record<SortField, string> = {
    [SortField.UPLOAD_TIME]: '上传时间',
    [SortField.FILE_SIZE]: '文件大小',
    [SortField.DURATION]: '媒体时长',
    [SortField.FILE_NAME]: '名称',
    [SortField.QUOTE_COUNT]: '引用次数',
  }

  export enum SortOrder {
    ASC = 'asc',
    DESC = 'desc',
  }
  export interface SortDirectionOption {
    label: string
    order: SortOrder
  }

  export interface SortOption {
    label: string
    field: SortField
    directions: SortDirectionOption[]
  }
  export enum MediaStyle {
    HORIZONTAL = 'horizontal',
    VERTICAL = 'vertical',
  }
  export interface MaterialMediaParams extends PaginationParams {
    projectId: number
    folderUuid: string
    sortField: SortField // 排序字段
    sortOrder: SortOrder // 排序方向
    createAtRange?: string[] // 创建时间范围
    durationRange?: number[] // 时长范围
    useCountRange?: number | undefined // 合成次数
    quoteCountRange?: number | undefined // 引用次数
    keyword?: string | undefined
    resType?: 0 | MediaType
  }
  export interface Media {
    codecs?: string
    cover?: string
    coverFrame?: null
    createTime?: number
    dar?: null
    duration?: number
    fileId: string
    fileName: string
    fileSize?: number
    folderUuid: string
    hdrSetting?: number
    height?: null
    lnp?: number
    lnPath?: null
    projectId?: number
    quoteCount?: number
    reason?: null
    resType?: number
    rotate?: number
    sceneTags?: null
    sceneType?: number
    segment?: null
    status?: number
    tags?: null
    taskNo?: null
    trackFrame?: null
    url?: string
    useCount?: number
    width?: null
    //文件夹
    childrenFolder?: number
    mediaNum?: number
  }
}
