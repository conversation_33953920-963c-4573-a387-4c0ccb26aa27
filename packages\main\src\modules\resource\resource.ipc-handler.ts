import { Inject, Injectable } from '@nestjs/common'
import { ResourceService } from './resource.service.js'
import { BaseIPCHandler } from '@/infra/types/BaseIPCHandler.js'

@Injectable()
export class ResourceIpcHandler extends BaseIPCHandler<'resource'> {

  protected readonly platformPrefix = 'resource'

  constructor(
    @Inject(ResourceService) private readonly service: ResourceService
  ) {
    super()
  }

  /**
   * 注册所有通用资源处理程序
   */
  registerAll(): void {
    // 通用资源获取，需要明确指定类型
    this.registerHandler('fetch', async ({ url, type, version, customExt }) => {
      if (!url || !type) {
        throw new Error('URL和资源类型不能为空')
      }
      return this.service.fetchAndCacheResource(url, type, version || '1.0', customExt)
    })

    // 缓存清理
    this.registerHandler('clean', async (params: { maxAgeSeconds?: number; maxCacheSizeMB?: number } = {}) => {
      return this.service.cleanResourceCache(params.maxAgeSeconds, params.maxCacheSizeMB)
    })

    // 获取资源信息
    this.registerHandler('getInfo', async ({ url }) => {
      if (!url) {
        throw new Error('URL不能为空')
      }
      return this.service.getResourceCacheEntry(url)
    })

    // 获取资源路径（不下载）
    this.registerHandler('getPath', async ({ url, type }) => {
      if (!url || !type) {
        throw new Error('URL和资源类型不能为空')
      }
      return this.service.getResourceCachePath(url, type)
    })

    // 获取所有已缓存资源
    this.registerHandler('getAllCached', async () => {
      return this.service.getAllCachedResources()
    })

    // 检查资源是否已缓存
    this.registerHandler('isCached', async ({ url }) => {
      if (!url) {
        throw new Error('URL不能为空')
      }
      return this.service.isResourceCached(url)
    })
  }
}
