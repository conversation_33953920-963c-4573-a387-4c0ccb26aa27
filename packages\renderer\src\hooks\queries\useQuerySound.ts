import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { ResourceModule } from '@/libs/request/api/resource'
import { useInfiniteQuery } from '../useInfiniteQuery'
import { BaseResourceQueryParams, SoundResource } from '@/types/resources'

export const useQuerySoundCategory = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.VOICE_CATEGORY],
    queryFn: () => ResourceModule.voice.category(),
  })
}

export function useQuerySoundList(params: BaseResourceQueryParams) {
  return useQuery({
    queryKey: [QUERY_KEYS.VOICE_LIST, params],
    queryFn: () => ResourceModule.voice.list(params),
    enabled: params.categoryIds ? params.categoryIds.length > 0 : true,
  })
}

/**
 * 使用无限查询获取音效列表，支持无限滚动加载
 * @param params 查询参数，包括分类ID和每页大小
 * @returns 无限查询结果
 */
export const useInfiniteQuerySoundList = (params: BaseResourceQueryParams) => {
  return useInfiniteQuery<SoundResource.Sound>(
    [QUERY_KEYS.VOICE_LIST],
    ResourceModule.voice.list,
    params,
    {
      pageSize: params.pageSize || 12,
      enabled: params.categoryIds ? params.categoryIds.length > 0 : true,
    }
  )
}

/**
 * 使用无限查询获取收藏的音效列表，支持无限滚动加载
 * @param params 查询参数
 * @returns 无限查询结果
 */
export const useInfiniteQuerySoundCollectedList = (params: BaseResourceQueryParams) => {
  return useInfiniteQuery<SoundResource.Sound>(
    [QUERY_KEYS.VOICE_COLLECTED],
    ResourceModule.voice.collected,
    params,
    {
      pageSize: params.pageSize || 12,
    }
  )
}

/**
 * 统一的音效查询hook，根据选择的分类自动决定使用哪个接口
 * @param params 查询参数，包括选中的分类ID
 * @returns 无限查询结果
 */
export const useInfiniteQuerySoundUnified = (params: BaseResourceQueryParams & { selectedCategory?: string }) => {
  const { selectedCategory, ...queryParams } = params

  // 判断是否为收藏分类
  const isCollectedCategory = selectedCategory === 'collected'

  // 根据分类决定查询参数
  const finalParams = isCollectedCategory
    ? queryParams // 收藏列表不需要 categoryIds
    : {
      ...queryParams,
      // 全部分类不传 categoryIds，其他分类传递对应的 categoryIds
      categoryIds: (!selectedCategory || selectedCategory === 'all')
        ? undefined
        : [selectedCategory]
    }

  // 根据分类决定使用哪个查询
  if (isCollectedCategory) {
    return useInfiniteQuery<SoundResource.Sound>(
      [QUERY_KEYS.VOICE_COLLECTED, finalParams],
      ResourceModule.voice.collected,
      finalParams,
      {
        pageSize: finalParams.pageSize || 12,
      }
    )
  } else {
    return useInfiniteQuery<SoundResource.Sound>(
      [QUERY_KEYS.VOICE_LIST, finalParams],
      ResourceModule.voice.list,
      finalParams,
      {
        pageSize: finalParams.pageSize || 12,
        enabled: finalParams.categoryIds ? finalParams.categoryIds.length > 0 : true
      }
    )
  }
}

