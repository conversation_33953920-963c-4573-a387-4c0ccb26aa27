import React, { PropsWithChildren, useCallback, useEffect, useState } from 'react'
import {
  CachedOverlaysContext as CachedOverlaysContext1,
  CachedOverlaysContextValues,
  useEditorContext
} from '@rve/editor/contexts'
import { useCalculateRenderableOverlays } from '@rve/editor/hooks/useCalculateRenderableOverlays.ts'
import { RenderableOverlay } from '@app/rve-shared/types'
import { merge } from 'lodash'

export const CachedOverlaysProvider: React.FC<PropsWithChildren> = ({ children }) => {
  const { updateOverlay } = useEditorContext()
  const calculateFinalOverlays = useCalculateRenderableOverlays()

  const [localOverlays, setLocalOverlays] = useState<RenderableOverlay[]>(() => calculateFinalOverlays())

  useEffect(() => {
    setLocalOverlays(calculateFinalOverlays())
  }, [calculateFinalOverlays])

  const handleUpdateOverlay = useCallback<CachedOverlaysContextValues['requestUpdate']>(
    (id, updater, commit = false) => {
      const index = localOverlays.findIndex(o => o.id === id)
      if (index === -1) return localOverlays
      const target = localOverlays[index]!

      const updatedOverlay = (
        typeof updater === 'function'
          ? updater(target as any)
          : merge({}, target, updater)
      ) as RenderableOverlay

      if (commit) {
        updateOverlay(updatedOverlay.id, () => updatedOverlay)
      }

      setLocalOverlays([
        ...localOverlays.slice(0, index),
        updatedOverlay,
        ...localOverlays.slice(index + 1)
      ])
    },
    [localOverlays]
  )

  return (
    <CachedOverlaysContext1
      value={{
        overlays: localOverlays,
        requestUpdate: handleUpdateOverlay
      }}
    >
      {children}
    </CachedOverlaysContext1>
  )
}
