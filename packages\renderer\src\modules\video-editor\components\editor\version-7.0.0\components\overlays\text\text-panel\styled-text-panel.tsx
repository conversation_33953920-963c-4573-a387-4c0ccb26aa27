import React, { useCallback } from 'react'
import { useOverlayEditing } from '../../../../contexts'
import { TextOverlay } from '@app/rve-shared/types'
import { FontStyleResource } from '@/types/resources'

import FontStyleSelector from '../font-style-selector'

export const StyledTextPanel = () => {
  const { localOverlay, requestUpdate } = useOverlayEditing<TextOverlay>()

  const handleFontStyleSelect = useCallback(
    async (fontStyleResource: FontStyleResource.FontStyle, previewOverlay: TextOverlay) => {
      if (!localOverlay) return

      try {
        console.log('[花体字编辑] 开始更新花体字样式:', fontStyleResource.content.fontName)

        // 使用预览覆盖层的样式更新当前覆盖层
        const updatedOverlay: Partial<TextOverlay> = {
          src: fontStyleResource.content.fontPath,
          styles: {
            ...localOverlay.styles,
            // 更新字体相关样式
            fontFamily: previewOverlay.styles.fontFamily,
            fontStyle: previewOverlay.styles.fontStyle,
            fontWeight: previewOverlay.styles.fontWeight,
            // 更新颜色样式
            color: previewOverlay.styles.color,
            backgroundColor: previewOverlay.styles.backgroundColor,
            // 更新轮廓样式
            strokeEnabled: previewOverlay.styles.strokeEnabled,
            strokeWidth: previewOverlay.styles.strokeWidth,
            strokeColor: previewOverlay.styles.strokeColor,
            // 更新阴影样式
            shadowEnabled: previewOverlay.styles.shadowEnabled,
            shadowDistance: previewOverlay.styles.shadowDistance,
            shadowAngle: previewOverlay.styles.shadowAngle,
            shadowBlur: previewOverlay.styles.shadowBlur,
            shadowColor: previewOverlay.styles.shadowColor,
            shadowOpacity: previewOverlay.styles.shadowOpacity,
            // 更新下划线样式
            underlineEnabled: previewOverlay.styles.underlineEnabled,
          }
        }

        requestUpdate(updatedOverlay)

        console.log('[花体字编辑] 花体字样式更新成功')
      } catch (error) {
        console.error('[花体字编辑] 更新花体字样式失败:', error)
      }
    },
    [localOverlay, requestUpdate]
  )

  if (!localOverlay) {
    return (
      <div className="flex items-center justify-center h-full text-gray-500">
        请先选择一个文本图层
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">

      <div className="flex-1 overflow-hidden">
        <FontStyleSelector
          onFontStyleSelect={handleFontStyleSelect}
          className="h-full flex flex-wrap"
        />
      </div>
    </div>
  )
}
