import { useCallback } from 'react'
import { ResourceType, } from '@app/shared/types/resource-cache.types.ts'
import { cacheManager } from '@/libs/cache/cache-manager.ts'
import { PasterResource } from '@/types/resources.ts'

// 用于跟踪正在加载的资源，这个仍然保留为全局变量，因为它不需要触发重新渲染
const loadingResources: Record<string, Record<string, boolean>> = {}

// 初始化各类资源的加载状态对象
Object.values(ResourceType).forEach(type => {
  loadingResources[type] = {}
})

// 贴纸三层加载状态管理
const stickerLoadingStates: Record<string | number, PasterResource.StickerLoadingState> = {}

// 预加载的图片缓存
const preloadedImages: Record<string, HTMLImageElement> = {}

type DownloadResourceToCacheProps = {
  url: string;
  resourceType: ResourceType;
  version?: string;
  customExt?: string;
  id?: string | number;
}

export const useResource = () => {
  /**
   * 检查资源是否已缓存
   */
  const isResourceCached = useCallback(async (type: ResourceType, url: string): Promise<boolean> => {
    // 严格的参数验证
    if (!type || !url) {
      console.warn('[资源缓存] 参数无效:', { type, url })
      return false
    }

    // 确保参数是可序列化的
    if (typeof type !== 'string' || typeof url !== 'string') {
      console.error('[资源缓存] 参数类型错误:', {
        type: typeof type,
        url: typeof url,
        type_value: type,
        url_value: url
      })
      return false
    }

    // 验证 ResourceType 枚举值
    if (!Object.values(ResourceType).includes(type)) {
      console.error('[资源缓存] 无效的资源类型:', type)
      return false
    }

    // 验证 URL 格式
    try {
      new URL(url)
    } catch (urlError) {
      // 如果不是完整URL，检查是否是相对路径
      if (!url.startsWith('/') && !url.startsWith('./') && !url.startsWith('../')) {
        console.error(`[资源缓存] 无效的URL格式，${type}:`, url)
        return false
      }
    }

    // 首先检查 IndexedDB 缓存
    const isCachedInDB = await cacheManager.resource.isResourceCached(type, url)

    if (isCachedInDB) {
      return true
    }

    // 如果缓存中没有，则向主进程查询
    try {
      // 主进程的 isCached 方法只需要 url 参数
      const isCached = await window.resource.isCached({ url })

      // 如果资源已缓存但本地状态未更新，则更新本地状态
      if (isCached) {
        // 获取缓存路径
        const path = await window.resource.getPath({ url, type })

        if (path) {
          // 更新 IndexedDB 缓存
          await cacheManager.resource.setResourceCache(type, url, path)
        }
      }

      return isCached
    } catch (error) {
      console.error('[资源缓存] 检查资源缓存状态失败:', error)
      console.error('[资源缓存] 参数:', { type, url })

      // 检查是否是序列化错误
      if (error && typeof error === 'object') {
        const errorMessage = (error as any).message || String(error)
        if (errorMessage.includes('object Object') || errorMessage.includes('序列化')) {
          console.error('[资源缓存] 可能的序列化错误，检查参数类型:', {
            typeOf_type: typeof type,
            typeOf_url: typeof url,
            type_value: type,
            url_value: url
          })
        }
      }

      return false
    }
  }, [])

  /**
   * 设置资源加载状态
   * 内部方法，用于统一管理资源加载状态
   */
  const setResourceLoadingState = useCallback(async (type: ResourceType, url: string, isLoading: boolean) => {
    // 更新全局变量（保留用于向后兼容）
    loadingResources[type][url] = isLoading

    // 更新 IndexedDB 缓存
    await cacheManager.loadingState.setResourceLoadingState(type, url, isLoading)
  }, [])

  /**
   * 下载资源到本地缓存
   */
  const downloadResourceToCache = async (data: DownloadResourceToCacheProps): Promise<string | null> => {
    // 参数验证
    if (!data || !data.url || !data.resourceType) {
      console.error('[资源下载] 参数无效:', data)
      return null
    }

    // 验证 ResourceType 枚举值
    if (!Object.values(ResourceType).includes(data.resourceType)) {
      console.error('[资源下载] 无效的资源类型:', data.resourceType)
      return null
    }

    // 确保参数是可序列化的
    const params = {
      url: String(data.url),
      type: data.resourceType, // 保持 ResourceType 类型
      version: data.version ? String(data.version) : '1.0',
      customExt: data.customExt ? String(data.customExt) : undefined
    }

    try {
      // 设置加载状态
      await setResourceLoadingState(data.resourceType, data.url, true)

      // 获取本地缓存路径，不存在/版本不一致则下载
      const localPath = await window.resource.fetch(params)

      console.log(`获取: ${data.url} -> ${localPath}`)

      if (localPath) {
        // 更新 IndexedDB 缓存
        await cacheManager.resource.setResourceCache(data.resourceType, data.url, localPath, data.version)
      }

      return localPath
    }
    catch (error) {
      console.error('获取资源路径失败:', error)
      return null
    }
    finally {
      // 清除加载状态
      await setResourceLoadingState(data.resourceType, data.url, false)
    }
  }

  /**
   * 同步获取资源的本地缓存路径（如果已缓存）
   */
  const getResourcePathSync = useCallback((type: ResourceType, url: string): string | null => {
    return cacheManager.resource.getResourcePathSync(type, url)
  }, [])

  /**
   * 获取资源的详细信息
   */
  const getResourceInfo = async (data: { url: string }): Promise<any | null> => {
    try {
      // 获取资源信息
      const resourceInfo = await window.resource.getInfo({
        url: data.url
      })

      return resourceInfo
    } catch (error) {
      console.error('获取资源信息失败:', error)
      return null
    }
  }

  /**
   * 清除资源缓存
   */
  const clearResourceCache = useCallback(async (type?: ResourceType, url?: string) => {
    await cacheManager.resource.clearResourceCache(type, url)
  }, [])

  /**
   * 检查特定资源是否正在加载
   */
  const isResourceLoading = useCallback(async (type: ResourceType, url: string): Promise<boolean> => {
    // 首先检查 IndexedDB 缓存中的加载状态
    const isLoading = await cacheManager.loadingState.getResourceLoadingState(type, url)

    // 如果 IndexedDB 中有明确的加载状态，则使用它
    if (isLoading !== undefined) {
      return !!isLoading
    }

    // 否则回退到全局变量
    return !!loadingResources[type][url]
  }, [])

  /**
   * 检查特定项是否正在加载（通过 id）
   */
  const isItemLoading = useCallback(async (id?: string | number): Promise<boolean> => {
    if (!id) return false

    // 检查是否有任何资源类型下有此 id 的资源正在加载
    return await cacheManager.loadingState.isItemLoading(id)
  }, [])

  /**
   * 获取特定类型资源的缓存
   */
  const getResourceCache = useCallback(async (type: ResourceType): Promise<Record<string, string>> => {
    return await cacheManager.resource.getResourceCacheByType(type)
  }, [])

  /**
   * 获取贴纸的加载状态
   */
  const getStickerLoadingState = useCallback(async (stickerId: string | number): Promise<PasterResource.StickerLoadingState> => {
    const state = await cacheManager.stickerLoading.getStickerLoadingState(stickerId)

    // 转换为 PasterResource.StickerLoadingState 格式
    return {
      coverId: state.coverId,
      coverLoaded: state.coverLoaded,
      thumbLoaded: state.thumbLoaded,
      thumbLoading: state.thumbLoading,
      fileLoaded: state.fileLoaded,
      fileLoading: state.fileLoading,
      currentLayer: state.currentLayer as PasterResource.LoadingLayer
    }
  }, [])

  /**
   * 更新贴纸加载状态
   */
  const updateStickerLoadingState = useCallback(async (
    stickerId: string | number,
    updates: Partial<PasterResource.StickerLoadingState>
  ) => {
    // 转换为 IndexedDB 格式并更新
    await cacheManager.stickerLoading.updateStickerLoadingState(stickerId, {
      ...updates,
      currentLayer: updates.currentLayer as 'cover' | 'thumb' | 'file'
    })

    // 同时更新内存状态（保留向后兼容）
    const currentState = await getStickerLoadingState(stickerId)
    stickerLoadingStates[stickerId] = { ...currentState, ...updates }
  }, [getStickerLoadingState])

  /**
   * 预加载图片（用于悬停预览）
   */
  const preloadImage = useCallback(async (url: string): Promise<HTMLImageElement> => {
    // 如果已经预加载过，直接返回
    if (preloadedImages[url]) {
      return preloadedImages[url]
    }

    // 检查 IndexedDB 中是否已缓存
    const isPreloaded = await cacheManager.image.isImagePreloaded(url)
    if (isPreloaded) {
      const cachedData = await cacheManager.image.getPreloadedImage(url)
      if (cachedData) {
        const img = new Image()
        img.src = cachedData
        preloadedImages[url] = img
        return img
      }
    }

    return new Promise((resolve, reject) => {
      const img = new Image()
      img.onload = async () => {
        preloadedImages[url] = img

        // 将图片数据缓存到 IndexedDB
        try {
          await cacheManager.image.setPreloadedImage(url, img.src)
        } catch (error) {
          console.warn('缓存预加载图片失败:', error)
        }

        resolve(img)
      }
      img.onerror = reject
      img.src = url
    })
  }, [])

  /**
   * 贴纸悬停预览加载
   */
  const loadStickerPreview = useCallback(async (
    sticker: PasterResource.Paster | PasterResource.PasterLocal
  ): Promise<void> => {
    const stickerId = 'fileId' in sticker ? sticker.fileId : sticker.id
    const thumbUrl = sticker.content.thumbUrl

    // 获取当前状态
    const currentState = await getStickerLoadingState(stickerId)

    // 如果已经加载过或正在加载，直接返回
    if (currentState.thumbLoaded || currentState.thumbLoading) {
      return
    }

    // 设置加载状态
    await updateStickerLoadingState(stickerId, {
      thumbLoading: true,
      currentLayer: PasterResource.LoadingLayer.THUMB
    })

    try {
      // 预加载缩略图
      await preloadImage(thumbUrl)

      // 更新状态为已加载
      await updateStickerLoadingState(stickerId, {
        thumbLoaded: true,
        thumbLoading: false
      })

      console.log(`贴纸预览加载完成: ${thumbUrl}`)
    } catch (error) {
      console.error('贴纸预览加载失败:', error)

      // 重置加载状态
      await updateStickerLoadingState(stickerId, {
        thumbLoading: false,
        currentLayer: PasterResource.LoadingLayer.COVER
      })
    }
  }, [getStickerLoadingState, updateStickerLoadingState, preloadImage])

  /**
   * 贴纸完整资源缓存（用于编辑器）
   */
  const cacheStickerForEditor = useCallback(async (
    sticker: PasterResource.Paster | PasterResource.PasterLocal
  ): Promise<string | null> => {
    const stickerId = 'fileId' in sticker ? sticker.fileId : sticker.id
    const fileUrl = sticker.content.fileUrl

    // 获取当前状态
    const currentState = await getStickerLoadingState(stickerId)

    // 如果已经缓存过，直接返回路径
    if (currentState.fileLoaded) {
      return getResourcePathSync(ResourceType.STICKER, fileUrl)
    }

    // 如果正在加载，等待完成
    if (currentState.fileLoading) {
      // 这里可以实现等待逻辑，或者直接返回null
      return null
    }

    // 设置加载状态
    await updateStickerLoadingState(stickerId, {
      fileLoading: true,
      currentLayer: PasterResource.LoadingLayer.FILE
    })

    try {
      // 下载并缓存完整资源
      const localPath = await downloadResourceToCache({
        url: fileUrl,
        resourceType: ResourceType.STICKER,
        id: stickerId
      })

      // 更新状态为已缓存
      await updateStickerLoadingState(stickerId, {
        fileLoaded: true,
        fileLoading: false
      })

      console.log(`贴纸完整资源缓存完成: ${fileUrl} -> ${localPath}`)
      return localPath
    } catch (error) {
      console.error('贴纸完整资源缓存失败:', error)

      // 重置加载状态
      await updateStickerLoadingState(stickerId, {
        fileLoading: false
      })

      return null
    }
  }, [getStickerLoadingState, updateStickerLoadingState, downloadResourceToCache, getResourcePathSync])

  return {
    downloadResourceToCache,
    getResourcePathSync,
    isResourceCached,
    getResourceInfo,
    clearResourceCache,
    isResourceLoading,
    isItemLoading,
    getResourceCache,
    setResourceLoadingState,
    // 三层加载策略相关方法
    getStickerLoadingState,
    updateStickerLoadingState,
    loadStickerPreview,
    cacheStickerForEditor,
    preloadImage
  }
}
