import { createContext, useContext } from 'react'

export interface AssetLoadingContextType {
  isLoadingAssets: boolean
  isInitialLoad: boolean
  handleAssetLoadingChange: (overlayId: number, isLoading: boolean) => void
  setInitialLoadComplete: () => void
}

export const AssetLoadingContext = createContext<AssetLoadingContextType | undefined>(
  undefined,
)

export const useAssetLoading = () => {
  const context = useContext(AssetLoadingContext)
  if (!context) {
    throw new Error(
      'useAssetLoading must be used within an AssetLoadingProvider',
    )
  }
  return context
}
