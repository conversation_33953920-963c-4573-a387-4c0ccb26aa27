import { ParamsForComponent, VirtualizableComponentKeys, VirtualTab } from '@/libs/stores/useVirtualTabsStore.ts'
import React from 'react'

export const VirtualTabContext = React.createContext<VirtualTab>(null as any)

export function useVirtualTab<T extends VirtualizableComponentKeys>(_: T) {
  const { params = {}, componentKey, ...context } = React.useContext(VirtualTabContext) || {}

  if (componentKey !== _) {
    throw new Error(`You are using useVirtualTab("${_}") but the current tab is "${componentKey}"`)
  }

  return {
    ...context,
    params: params as ParamsForComponent<T>
  }
}
