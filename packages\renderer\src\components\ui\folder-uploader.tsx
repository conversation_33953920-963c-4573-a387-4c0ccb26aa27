import React, { use<PERSON><PERSON>back, useMemo, useRef, useState } from 'react'
import { AlertCircle, CheckCircle, Folder, FolderOpen, Loader2, X } from 'lucide-react'
import { cn } from '@/components/lib/utils'
import { Button } from './button'
import { uploadBufferViaIPC, UploadModule } from '@/libs/request/upload'
import { ResourceModule } from '@/libs/request/api/resource'
import { md5 } from '@/libs/tools/md5.ts'

declare module 'react' {
  interface InputHTMLAttributes<T> extends AriaAttributes, DOMAttributes<T> {
    webkitdirectory?: boolean
    directory?: boolean
  }
}

export interface FolderUploadedFile {
  id: string
  file: File
  relativePath: string
  status: 'uploading' | 'success' | 'error'
  error?: string
  url?: string
  fileName?: string
  progress?: number // 上传进度 (0-1)
  objectId?: string
  fileMd5?: string
  folderUuid?: string
}

export interface FolderUploaderProps {
  folderUuid: string
  fileTypes?: string[]
  maxFiles?: number
  maxSize?: number
  onUpload?: (files: FolderUploadedFile[]) => void
  onError?: (error: string) => void
  onProgress?: (current: number, total: number) => void
  className?: string
  disabled?: boolean
  children?: React.ReactNode
  buttonText?: string
  buttonClassName?: string
  showFileList?: boolean
  isShowUploadedFiles?: boolean
  dirPrefix?: string
  resourceType: 'material' | 'paster' | 'music' | 'voice'
}

const FolderUploader: React.FC<FolderUploaderProps> = ({
  folderUuid,
  resourceType = 'material',
  fileTypes = ['video/*', 'image/*'],
  maxFiles = 100,
  maxSize = 100 * 1024 * 1024, // 100MB
  onUpload,
  onError,
  onProgress,
  className,
  disabled = false,
  children,
  buttonText = '选择文件夹',
  buttonClassName,
  showFileList = true,
  isShowUploadedFiles = true,
  dirPrefix = 'user-uploads/',
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<FolderUploadedFile[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [newFolderId, setNewFolderId] = useState('')
  const folderInputRef = useRef<HTMLInputElement>(null)

  // 计算上传状态
  const uploadStats = useMemo(() => {
    const total = uploadedFiles.length
    const uploading = uploadedFiles.filter(f => f.status === 'uploading').length
    const success = uploadedFiles.filter(f => f.status === 'success').length
    const error = uploadedFiles.filter(f => f.status === 'error').length

    return { total, uploading, success, error }
  }, [uploadedFiles])

  // 从文件夹中提取所有符合条件的文件
  const extractFilesFromFolder = useCallback(
    (files: FileList): File[] => {
      const extractedFiles: File[] = []

      for (let i = 0; i < files.length; i++) {
        const file = files[i]

        // 检查文件类型
        const isValidType = fileTypes.some(type => {
          if (type.endsWith('/*')) {
            const category = type.split('/')[0]
            return file.type.startsWith(category + '/')
          }
          return file.type === type
        })

        // 检查文件大小
        if (isValidType && file.size <= maxSize) {
          extractedFiles.push(file)
        }
      }

      return extractedFiles
    },
    [fileTypes, maxSize],
  )

  // 上传单个文件到 OSS
  const uploadFileToOSS = useCallback(
    async (uploadedFile: FolderUploadedFile, newFolderId: string) => {
      try {
        const arrayBuffer = await uploadedFile.file.arrayBuffer()
        const fileMd5 = md5(arrayBuffer)
        const result = await uploadBufferViaIPC(
          arrayBuffer,
          uploadedFile.file.name,
          newFolderId,
          fileMd5,
          UploadModule.media,
          {
            dirPrefix,
            onProgress: (progress: number) => {
              // 更新文件的上传进度
              setUploadedFiles(prev => {
                const updated = prev.map(f => (f.id === uploadedFile.id ? { ...f, progress: progress } : f))
                return updated
              })
            },
          },
        )

        // 更新文件状态为成功
        setUploadedFiles(prev => {
          const updated = prev.map(f =>
            f.id === uploadedFile.id
              ? {
                ...f,
                status: 'success' as const,
                url: result.url,
                fileName: result.fileName,
                objectId: result.objectId,
                folderUuid: result.folderUuid,
                fileMd5: fileMd5,
                progress: 1,
              }
              : f,
          )
          return updated
        })
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '上传失败'

        // 更新文件状态为失败
        setUploadedFiles(prev => {
          const updated = prev.map(f =>
            f.id === uploadedFile.id
              ? {
                ...f,
                status: 'error' as const,
                error: errorMessage,
              }
              : f,
          )
          return updated
        })

        onError?.(errorMessage)
      }
    },
    [folderUuid, dirPrefix, onError],
  )

  // 处理文件夹选择
  const handleFolderSelect = useCallback(() => {
    console.log('点击选择文件夹按钮')
    if (disabled || isUploading) {
      console.log('按钮被禁用或正在上传中')
      return
    }
    console.log('触发文件夹选择对话框')
    folderInputRef.current?.click()
  }, [disabled, isUploading])

  // 处理文件夹输入变化
  const handleFolderInputChange = useCallback(
    async (event: React.ChangeEvent<HTMLInputElement>) => {
      const files = event.target.files
      console.log('文件夹选择事件触发，文件数量:', files?.length)

      if (!files || files.length === 0) {
        console.log('没有选择文件或文件列表为空')
        return
      }

      const firstFile = files[0] as any
      const folderPath = firstFile.webkitRelativePath || firstFile.name
      const folderName = folderPath.split('/')[0]
      console.log('选择的文件夹名:', folderName)

      console.log(
        '选择的文件列表:',
        Array.from(files).map(f => ({ name: f.name, path: (f as any).webkitRelativePath })),
      )

      const extractedFiles = extractFilesFromFolder(files)
      console.log('过滤后的文件数量:', extractedFiles.length)

      if (extractedFiles.length === 0) {
        console.log('没有找到符合条件的文件')
        onError?.('文件夹中没有找到支持的文件类型')
        return
      }

      if (extractedFiles.length > maxFiles) {
        console.log(`文件数量超限: ${extractedFiles.length} > ${maxFiles}`)
        onError?.(`文件夹中有 ${extractedFiles.length} 个文件，超过最大限制 ${maxFiles} 个`)
        return
      }

      // 创建上传文件对象
      const newFiles: FolderUploadedFile[] = extractedFiles.map(file => {
        const id = Math.random().toString(36).substring(2, 11)
        const relativePath = 'webkitRelativePath' in file ? (file as any).webkitRelativePath : (file as File).name
        return {
          id,
          file,
          relativePath,
          status: 'uploading' as const,
          progress: 0,
        }
      })

      setUploadedFiles(newFiles)
      setIsUploading(true)

      let res: string
      if (resourceType === 'paster') {
        res = await ResourceModule.paster.dirCreate({ folderName, parentId: folderUuid })
      } else if (resourceType === 'music') {
        res = await ResourceModule.music.dirCreate({ folderName, parentId: folderUuid })
      } else if (resourceType === 'voice') {
        res = await ResourceModule.voice.dirCreate({ folderName, parentId: folderUuid })
      } else {
        res = await ResourceModule.directory.create({ folderName, parentId: folderUuid })
      }
      setNewFolderId(res)

      // 依次上传所有文件
      for (let i = 0; i < newFiles.length; i++) {
        const file = newFiles[i]
        onProgress?.(i + 1, newFiles.length)
        await uploadFileToOSS(file, res)
      }

      setIsUploading(false)

      // 上传完成回调
      const finalFiles = await new Promise<FolderUploadedFile[]>(resolve => {
        setUploadedFiles(prev => {
          resolve(prev)
          return prev
        })
      })

      onUpload?.(finalFiles)

      // 清空input值，允许重复选择同一个文件夹
      event.target.value = ''
    },
    [extractFilesFromFolder, maxFiles, onError, uploadFileToOSS, onProgress, onUpload],
  )

  // 移除文件
  const removeFile = useCallback((fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId))
  }, [])

  // 清空所有文件
  const clearFiles = useCallback(() => {
    setUploadedFiles([])
  }, [])

  // 重试失败的文件
  const retryFailedFiles = useCallback(async () => {
    const failedFiles = uploadedFiles.filter(f => f.status === 'error')
    if (failedFiles.length === 0) return

    setIsUploading(true)

    // 重置失败文件的状态
    setUploadedFiles(prev =>
      prev.map(f => (f.status === 'error' ? { ...f, status: 'uploading' as const, error: undefined, progress: 0 } : f)),
    )

    // 重新上传失败的文件
    for (const file of failedFiles) {
      await uploadFileToOSS(file, newFolderId)
    }

    setIsUploading(false)
  }, [uploadedFiles, uploadFileToOSS])

  return (
    <div className={cn('space-y-4', className)}>
      {/* 隐藏的文件夹输入 */}
      <input
        ref={folderInputRef}
        type="file"
        {...({ webkitdirectory: '' } as any)}
        {...({ mozdirectory: '' } as any)}
        {...({ directory: '' } as any)}
        multiple
        style={{ display: 'none' }}
        onChange={handleFolderInputChange}
        disabled={disabled || isUploading}
        accept=""
      />

      {/* 自定义内容或默认按钮 */}
      {children ? (
        <div onClick={handleFolderSelect} className="cursor-pointer">
          {children}
        </div>
      ) : (
        <Button
          onClick={handleFolderSelect}
          disabled={disabled || isUploading}
          className={cn('flex items-center gap-2', buttonClassName)}
        >
          {isUploading ? <Loader2 className="w-4 h-4 animate-spin" /> : <FolderOpen className="w-4 h-4" />}
          {isUploading ? '上传中...' : buttonText}
        </Button>
      )}

      {/* 上传进度信息 */}
      {isShowUploadedFiles && uploadedFiles.length > 0 && (
        <div className="text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-center justify-between">
            <span>
              总计: {uploadStats.total} 个文件
              {uploadStats.success > 0 && ` | 成功: ${uploadStats.success}`}
              {uploadStats.error > 0 && ` | 失败: ${uploadStats.error}`}
              {uploadStats.uploading > 0 && ` | 上传中: ${uploadStats.uploading}`}
            </span>
            <div className="flex gap-2">
              {uploadStats.error > 0 && (
                <Button size="sm" variant="outline" onClick={retryFailedFiles} disabled={isUploading}>
                  重试失败
                </Button>
              )}
              <Button size="sm" variant="outline" onClick={clearFiles} disabled={isUploading}>
                清空列表
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 文件列表 */}
      {showFileList && uploadedFiles.length > 0 && (
        <div className="space-y-2 max-h-60 overflow-y-auto">
          {uploadedFiles.map(file => (
            <div key={file.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{file.file.name}</p>
                <p className="text-xs text-blue-600 dark:text-blue-400 truncate flex items-center">
                  <Folder className="w-3 h-3 mr-1 flex-shrink-0" />
                  {file.relativePath}
                </p>
                <div className="flex items-center gap-2 text-xs text-gray-500">
                  <span>{(file.file.size / 1024 / 1024).toFixed(2)} MB</span>
                  {file.status === 'uploading' && (
                    <span className="text-blue-500">
                      上传中... {file.progress ? `${Math.round(file.progress * 100)}%` : ''}
                    </span>
                  )}
                  {file.status === 'success' && (
                    <span className="text-green-500 flex items-center">
                      <CheckCircle className="w-3 h-3 mr-1" />
                      完成 {file?.url}
                    </span>
                  )}
                  {file.status === 'error' && (
                    <span className="text-red-500 flex items-center">
                      <AlertCircle className="w-3 h-3 mr-1" />
                      {file.error}
                    </span>
                  )}
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => removeFile(file.id)}
                className="text-gray-500 hover:text-red-500"
                disabled={isUploading}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

export { FolderUploader }
