import { useCallback, useState } from 'react'
import { Overlay } from '@app/rve-shared/types'
import { cloneDeep } from 'lodash'
import { useEditorContext } from '@rve/editor/contexts'
import { useOverlayHelper } from '@rve/editor/hooks/helpers/useOverlayHelper.ts'

export type TimelineClipboard = {
  /** 当前剪贴板中的 overlay */
  clipped: Overlay | null

  /** 将 overlay 复制到剪贴板 */
  clipOverlay(overlay: Overlay): void

  /** 清空剪贴板 */
  clearClipboard(): void

  /**
   * 在指定轨道的指定位置粘贴 overlay
   */
  pasteOverlay(trackIndex: number, startFrame: number): void
}

/**
 * Overlay 剪贴板 Hook
 * 提供复制、粘贴 overlay 的功能
 *
 * @returns OverlayClipboard 对象，包含剪贴板状态和操作方法
 *
 * @example
 * ```tsx
 * const clipboard = useOverlayClipboard()
 *
 * // 复制 overlay
 * clipboard.clipOverlay(selectedOverlay)
 *
 * ```
 */
export const useTimelineClipboard = (): TimelineClipboard => {
  const { deleteOverlay } = useEditorContext()
  const { appendOverlayToTrack } = useOverlayHelper()

  // 剪贴板状态 - 存储被复制的 overlay
  const [clipped, setClipped] = useState<Overlay | null>(null)

  /**
   * 将 overlay 复制到剪贴板
   * 使用深拷贝确保原始 overlay 不会被意外修改
   */
  const clipOverlay = useCallback((overlay: Overlay) => {
    // 深拷贝 overlay 以避免引用问题
    const clippedOverlay = cloneDeep(overlay)
    setClipped(clippedOverlay)
  }, [])

  /**
   * 清空剪贴板
   */
  const clearClipboard = () => setClipped(null)

  const pasteOverlay = useCallback(
    (trackIndex: number, startFrame: number) => {
      if (clipped) {
        setClipped(null)
        deleteOverlay(clipped.id)
        appendOverlayToTrack(trackIndex, clipped, startFrame)
      }
    },
    [clipped, deleteOverlay, appendOverlayToTrack]
  )

  return {
    clipped,
    clipOverlay,
    clearClipboard,
    pasteOverlay
  }
}
