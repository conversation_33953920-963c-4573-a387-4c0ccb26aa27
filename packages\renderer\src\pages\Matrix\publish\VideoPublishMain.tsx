import React, { useState, useEffect, useCallback, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { shuffle } from 'lodash'
import {  useSearchParams } from 'react-router'
import { useQuery } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@radix-ui/react-separator'
import useRequest from '@/hooks/useRequest'
import { MatrixModule } from '@/libs/request/api/matrix'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { SimpleVideo, VideoSelectionSection } from './components/VideoSelectionSection'

import { TitleManagementSection } from './components/TitleManagementSection'
import { AccountSelectionSection } from './components/AccountSelectionSection'
import { PublishSettingsSection } from './components/PublishSettingsSection'
import { CartInfoSection } from './components/CartInfoSection'
import { ScheduledPublishSection } from './components/ScheduledPublishSection'
import { LoopPublishSection } from './components/LoopPublishSection'
import { queryClient } from '@/main'
import { toast } from 'react-toastify'
import { LoaderCircle } from 'lucide-react'
import {
  Account,
  AccountProduct, CreateMatrixParams, DetailDOProduct, DetailDOS,
  DyPublishFormDetail,
  MountType,
  PublishMode,
  Setting,
  TimeType
} from '@/types/matrix/douyin.ts'
import { LocationInfoSection } from './components/LocationInfoSelector'

// Schema定义 - 简化版本，用于表单验证
const matrixPublishSchema = z.object({
  name: z.string().min(1, '计划名称不能为空').max(100, '计划名称不能超过100个字符'),
  marketingTarget: z.string().min(1, '营销目标不能为空'),
  videoList: z.array(z.object({
    cover: z.string().optional(),
    url: z.string()
  })).min(1, '请至少选择一个视频'),
  titles: z.array(z.object({
    value: z.string().min(1, '标题不能为空')
  })).optional(),
  accountIds: z.array(z.number()).min(1, '请至少选择一个账号'),
  publishMode: z.number(),
  mountType: z.number(),
  setting: z.number(),
  timeType: z.number(),
  poi: z.object({
    poiId: z.string().optional(),
    poiName: z.string().optional()
  }).optional(),
  accountProducts: z.array(z.object({
    accountId: z.number(),
    products: z.array(z.object({
      title: z.string(),
      url: z.string()
    }))
  })).optional(),
  timeSetting: z.object({
    publishTime: z.number().optional(),
    period: z.number().optional(),
    periodType: z.array(z.number()).optional(),
    loopDays: z.array(z.number()).optional(),
    loopTime: z.string().optional(),
    numEachDay: z.number().optional()
  }).optional()
}).refine(data => {
  // 如果挂载模式为位置，则位置名称为必填
  if (data.mountType === MountType.LOCATION) {
    return data.poi && data.poi.poiName && data.poi.poiName.length > 0
  }
  return true
}, {
  message: '选择位置挂载时，位置名称为必填项',
  path: ['poi', 'poiName'],
}).refine(data => {
  // 如果挂载模式为购物车，则至少需要配置一个账号的商品
  if (data.mountType === MountType.CART) {
    return data.accountProducts && data.accountProducts.length > 0
  }
  return true
}, {
  message: '选择购物车挂载时，请至少为一个账号配置商品',
  path: ['accountProducts'],
}).refine(data => {
  // 如果发布时间类型为定时，则发布时间为必填
  if (data.timeType === TimeType.SCHEDULED) {
    return data.timeSetting && data.timeSetting.publishTime
  }
  return true
}, {
  message: '选择定时发布时，发布时间为必填项',
  path: ['timeSetting', 'publishTime'],
}).refine(data => {
  // 如果发布时间类型为循环，则循环配置为必填
  if (data.timeType === TimeType.LOOP) {
    return data.timeSetting &&
           data.timeSetting.loopDays &&
           data.timeSetting.loopDays.length > 0 &&
           data.timeSetting.loopTime &&
           data.timeSetting.loopTime.length > 0
  }
  return true
}, {
  message: '选择循环发布时，循环日期和时间为必填项',
  path: ['timeSetting', 'loopDays'],
})

type MatrixPublishFormData = z.infer<typeof matrixPublishSchema>

export const VideoPublishMain = () => {
  const [searchParams] = useSearchParams()

  // 使用 useMemo 缓存路由参数，避免不必要的重新计算
  const { planId, draftId } = useMemo(() => ({
    planId: searchParams.get('planId'),
    draftId: searchParams.get('draftId')
  }), [searchParams])

  const [selectedAccounts, setSelectedAccounts] = useState<Account[]>([])
  const [selectedVideos, setSelectedVideos] = useState<SimpleVideo[]>([])
  const [currentDraftId, setCurrentDraftId] = useState<number | null>(null)

  const {
    register,
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    getValues,
    watch,
    reset
  } = useForm<MatrixPublishFormData>({
    resolver: zodResolver(matrixPublishSchema),
    defaultValues: {
      name: '',
      marketingTarget: '',
      videoList: [],
      titles: [{ value: '' }],
      accountIds: [],
      publishMode: PublishMode.CHECK_IN,
      mountType: MountType.NONE,
      setting: Setting.ONE_ACCOUNT_ONE_VIDEO,
      timeType: TimeType.IMMEDIATE,
      timeSetting: {
        publishTime: Date.now(),
        period: 0,
        periodType: [],
        loopDays: [Date.now()],
        loopTime: '08:00',
        numEachDay: 1
      }
    }
  })

  const timeType = watch('timeType')
  const mountType = watch('mountType')

  // 使用 useQuery 获取草稿或计划数据
  const { data: formData, isLoading: isFormDataLoading } = useQuery({
    queryKey: [QUERY_KEYS.MATRIX_DRAFT_DETAIL, QUERY_KEYS.MATRIX_PLAN_DETAIL, planId, draftId],
    queryFn: async () => {
      if (draftId) {
        const data = await MatrixModule.dyAccount.draftDetail(parseInt(draftId))
        setCurrentDraftId(data.id)
        return data
      } else if (planId) {
        return await MatrixModule.dyAccount.taskDetail(parseInt(planId))
      }
      return null
    },
    enabled: !!(draftId || planId),
    staleTime: 5 * 60 * 1000, // 5分钟内不重新请求
  })

  // 数据回显处理函数
  const loadFormData = useCallback((formData: DyPublishFormDetail) => {
    try {
      // 回显表单数据
      setValue('name', formData.name || '')
      setValue('marketingTarget', formData.marketingTarget || '')
      setValue('publishMode', formData.publishMode || PublishMode.CHECK_IN)
      setValue('mountType', formData.mountType || MountType.NONE)
      setValue('setting', formData.setting || Setting.ONE_ACCOUNT_ONE_VIDEO)
      setValue('timeType', formData.timeType || TimeType.IMMEDIATE)

      // 回显位置信息
      if (formData.poi) {
        setValue('poi', {
          poiId: formData.poi.poiId || '',
          poiName: formData.poi.poiName || ''
        })
      }

      // 回显时间设置
      if (formData.timeSetting) {
        setValue('timeSetting', {
          publishTime: formData.timeSetting.publishTime || Date.now(),
          period: formData.timeSetting.period || 0,
          periodType: formData.timeSetting.periodType || [],
          loopDays: formData.timeSetting.loopDays || [Date.now()],
          loopTime: formData.timeSetting.loopTime?.slice(0, 5) || '08:00', // 去掉秒数
          numEachDay: formData.timeSetting.numEachDay || 1
        })
      }

      // 回显标题数据
      if (formData.titles && formData.titles.length > 0) {
        setValue('titles', formData.titles.map(title => ({ value: title })))
      }

      // 回显账号产品数据
      if (formData.accountProducts) {
        setValue('accountProducts', formData.accountProducts)
      }

      // 回显账号数据
      if (formData.accountList && formData.accountList.length > 0) {
        setSelectedAccounts(formData.accountList)
        setValue('accountIds', formData.accountList.map(account => account.id))
      }

      // 回显视频数据
      if (formData.videoList && formData.videoList.length > 0) {
        const simpleVideos: SimpleVideo[] = formData.videoList
          .filter(video => video.url) // 过滤掉没有 url 的视频
          .map((video, index) => ({
            url: video.url!,
            cover: video.cover || '',
            name: video.name || `视频${index + 1}` // 使用真实名称或临时名称
          }))

        setSelectedVideos(simpleVideos)
        setValue('videoList', formData.videoList.filter(video => video.url).map(video => ({
          url: video.url!,
          cover: video.cover || ''
        })))
      }
    } catch (error) {
      console.error('回显表单数据失败:', error)
      toast('回显数据失败，请重试', { type: 'error' })
    }
  }, [setValue, setSelectedAccounts, setSelectedVideos])

  // 当数据加载完成后进行回显
  useEffect(() => {
    if (formData) {
      loadFormData(formData)
    }
  }, [formData, loadFormData])

  // 处理账号选择
  const handleAccountSelect = (accounts: Account[]) => {
    setSelectedAccounts(accounts)
    setValue('accountIds', accounts.map(account => account.id))
  }

  // 移除单个账号
  const handleRemoveAccount = (accountId: number) => {
    const updatedAccounts = selectedAccounts.filter(account => account.id !== accountId)
    handleAccountSelect(updatedAccounts)

    // 同时从账号商品配置中移除
    const currentAccountProducts = getValues('accountProducts') || []
    const updatedAccountProducts = currentAccountProducts.filter((ap: AccountProduct) => ap.accountId !== accountId)
    setValue('accountProducts', updatedAccountProducts)
  }

  // 处理视频选择
  const handleVideoSelect = (videos: SimpleVideo[]) => {
    setSelectedVideos(videos)
    setValue('videoList', videos.map(video => ({
      cover: video.customCover || video.cover || '',
      url: video.url
    })))
  }

  const handleRemoveVideo = (videoUrl: string) => {
    const updatedVideos = selectedVideos.filter(video => video.url !== videoUrl)
    handleVideoSelect(updatedVideos)
  }

  // 处理视频封面更新
  const handleUpdateVideoCover = (videoUrl: string, customCoverUrl: string) => {
    const updatedVideos = selectedVideos.map(video =>
      video.url === videoUrl
        ? { ...video, customCover: customCoverUrl }
        : video
    )
    handleVideoSelect(updatedVideos)
  }

  /**
   * 构建 detailDOS 数据
   * 根据发布设置、视频、标题、账号和商品信息进行排列组合
   * 关键特性
      不重复分配：在多视频模式下，每个视频只会分配给一个账号
      随机组合：使用 lodash.shuffle 确保视频分配的随机性
      标题优先级：视频自带标题 > 用户添加标题 > 默认标题
   */
  const buildDetailDOS = (
    accounts: Account[],
    videos: SimpleVideo[],
    titles: string[],
    accountProducts: AccountProduct[],
    setting: number,
    poiId: string,
    poiName: string
  ): DetailDOS[] => {
    const result: DetailDOS[] = []

    // 准备标题数组：优先使用视频自带标题，然后随机分配用户添加的标题
    const preparedTitles = [...titles] // 用户添加的标题
    const videoTitles: string[] = []

    // 为每个视频准备标题
    videos.forEach((video, index) => {
      if (video.name && video.name.trim()) {
        // 视频有自带标题，优先使用
        videoTitles[index] = video.name.trim()
      } else {
        // 视频没有标题，从用户添加的标题中随机选择
        if (preparedTitles.length > 0) {
          const randomIndex = Math.floor(Math.random() * preparedTitles.length)
          videoTitles[index] = preparedTitles[randomIndex]
          // 移除已使用的标题，避免重复（如果需要不重复的话）
          // preparedTitles.splice(randomIndex, 1)
        } else {
          // 如果没有可用标题，使用默认标题
          videoTitles[index] = `视频${index + 1}`
        }
      }
    })

    if (setting === Setting.ONE_ACCOUNT_ONE_VIDEO) {
      // 一个账号一个视频：账号和视频一一对应
      const minLength = Math.min(accounts.length, videos.length)

      for (let i = 0; i < minLength; i++) {
        const account = accounts[i]
        const video = videos[i]

        // 获取该账号的商品信息
        const accountProduct = accountProducts.find(ap => ap.accountId === account.id)
        const products: DetailDOProduct[] = accountProduct?.products.map(p => ({
          title: p.title,
          url: p.url
        })) || []

        result.push({
          accountId: account.id.toString(),
          cover: video.cover || '',
          description: '',
          poiId,
          poiName,
          products,
          title: videoTitles[i] || video.name || `视频${i + 1}`,
          url: video.url
        })
      }
    } else if (setting === Setting.ONE_ACCOUNT_MULTIPLE_VIDEOS) {
      // 一个账号多个视频：每个账号随机分配多个不重复的视频
      const shuffledVideos = shuffle([...videos])
      const videosPerAccount = Math.ceil(shuffledVideos.length / accounts.length)

      accounts.forEach((account, accountIndex) => {
        // 为每个账号分配视频
        const startIndex = accountIndex * videosPerAccount
        const endIndex = Math.min(startIndex + videosPerAccount, shuffledVideos.length)
        const accountVideos = shuffledVideos.slice(startIndex, endIndex)

        // 获取该账号的商品信息
        const accountProduct = accountProducts.find(ap => ap.accountId === account.id)
        const products: DetailDOProduct[] = accountProduct?.products.map(p => ({
          title: p.title,
          url: p.url
        })) || []

        accountVideos.forEach(video => {
          // 找到原始视频的索引以获取对应的标题
          const originalVideoIndex = videos.findIndex(v => v.url === video.url)

          result.push({
            accountId: account.id.toString(),
            cover: video.cover || '',
            description: '',
            poiId,
            poiName,
            products,
            title: videoTitles[originalVideoIndex] || video.name || `视频${originalVideoIndex + 1}`,
            url: video.url
          })
        })
      })
    }

    return result
  }

  const { mutate: handleSaveToDraft } = useRequest(
    () => {
      const data = getValues()
      const draftData = buildSubmitData(data)

      // 如果是编辑模式，携带草稿ID
      const submitData = currentDraftId
        ? { ...draftData, id: currentDraftId }
        : draftData

      return MatrixModule.dyAccount.saveDraft(submitData)
    },
    {
      actionName: currentDraftId ? '更新草稿' : '保存到草稿箱',
      onSuccess: () => {
        // 如果不是编辑模式，清空表单
        if (!currentDraftId) {
          reset()
          setSelectedAccounts([])
          setSelectedVideos([])
        }
        // 刷新草稿列表缓存
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATRIX_DRAFT_LIST] })
      }
    }
  )

  // 数据转换函数
  const buildSubmitData = (data: MatrixPublishFormData): CreateMatrixParams => {
    const titles = data.titles?.map(t => t.value) || []
    const detailDOS = buildDetailDOS(
      selectedAccounts,
      selectedVideos,
      titles,
      data.accountProducts || [],
      data.setting,
      data.poi?.poiId || '',
      data.poi?.poiName || ''
    )

    return {
      name: data.name,
      marketingTarget: data.marketingTarget,
      publishMode: data.publishMode,
      mountType: data.mountType,
      setting: data.setting,
      timeType: data.timeType,
      totalAccount: selectedAccounts.length,
      totalVideo: selectedVideos.length,
      titles,
      detailDOS,
      poi: {
        poiId: data.poi?.poiId || '',
        poiName: data.poi?.poiName || ''
      },
      accountIds: selectedAccounts.map(account => account.id),
      accountProducts: data.accountProducts || [],
      videoList: selectedVideos.map(video => ({
        cover: video.customCover || video.cover || '',
        url: video.url
      })),
      timeSetting: {
        publishTime: data.timeSetting?.publishTime || Date.now(),
        loopDays: data.timeSetting?.loopDays || [Date.now()],
        loopPeriod: 0,
        loopTime: data.timeSetting?.loopTime ? `${data.timeSetting?.loopTime}:00` : '08:00:00',
        numEachDay: data.timeSetting?.numEachDay || 1,
        period: data.timeSetting?.period || 0,
        periodType: data.timeSetting?.periodType || [],
      }
    }
  }

  const submitPublishMutation = useRequest(MatrixModule.dyAccount.publish, {
    actionName: '发布视频',
    onSuccess: () => {
      reset()
      setSelectedAccounts([])
      setSelectedVideos([])
    },
    onError: error => {
      console.error('发布失败:', error)
    }
  })

  const onSubmit = (data: MatrixPublishFormData) => {
    const submitData = buildSubmitData(data)
    submitPublishMutation.mutate(submitData)
    queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.DY_PUSH_PLAN_LIST] })
  }

  const onInvalid = (errors: any) => {
    console.log({ errors })

    const firstError = Object.values(errors)[0] as any
    const errorMessage = firstError?.message || '表单校验失败'

    toast(`表单校验失败: ${errorMessage}`, {
      type: 'warning'
    })
  }

  if (isFormDataLoading) {
    return (
      <div className="h-full w-full flex items-center justify-center">
        <LoaderCircle className="animate-spin" />
      </div>
    )
  }

  return (
    <div className=" p-6 h-full overflow-auto bg-background/50">
      <form onSubmit={handleSubmit(onSubmit, onInvalid)} className="space-y-8">
        <div className="space-y-6">
          <div className="flex items-center gap-2">
            <h2 className="text-lg font-semibold text-gradient-brand">基本信息</h2>
          </div>

          <div className="space-y-6">
            {/* 计划名称 */}
            <div className="space-y-2">
              <Label htmlFor="name" className="flex items-center gap-2">
                计划名称 <span className="text-red-500">*</span>
              </Label>
              <Input
                id="name"
                placeholder="请输入计划名称"
                {...register('name')}
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name.message}</p>
              )}
            </div>

            {/* 营销目标 */}
            <div className="space-y-2">
              <Label htmlFor="marketingTarget" className="flex items-center gap-2">
                营销目标 <span className="text-red-500">*</span>
              </Label>
              <Input
                id="marketingTarget"
                placeholder="请输入营销目标"
                {...register('marketingTarget')}
                className={errors.marketingTarget ? 'border-red-500' : ''}
              />
              {errors.marketingTarget && (
                <p className="text-sm text-red-500">{errors.marketingTarget.message}</p>
              )}
            </div>

            {/* 视频选择 */}
            <VideoSelectionSection
              selectedVideos={selectedVideos}
              onVideoSelect={handleVideoSelect}
              onRemoveVideo={handleRemoveVideo}
              onUpdateVideoCover={handleUpdateVideoCover}
              errors={errors}
            />

            {/* 动态标题 */}
            <TitleManagementSection
              control={control}
              errors={errors}
            />

            {/* 账号 */}
            <AccountSelectionSection
              selectedAccounts={selectedAccounts}
              onAccountSelect={handleAccountSelect}
              onRemoveAccount={handleRemoveAccount}
            />

            {/* 发布设置 */}
            <PublishSettingsSection
              control={control}
              errors={errors}
            />

            {/* 位置信息 - 当挂载模式为位置时显示 */}
            {mountType === MountType.LOCATION && (
              <LocationInfoSection
                control={control}
                errors={errors}
              />
            )}

            {/* 购物车信息 - 当挂载模式为购物车时显示 */}
            {mountType === MountType.CART && (
              <CartInfoSection
                control={control}
                errors={errors}
                selectedAccounts={selectedAccounts}
              />
            )}

            {/* 定时发布配置 - 当发布时间类型为定时时显示 */}
            {timeType === TimeType.SCHEDULED && (
              <ScheduledPublishSection
                control={control}
              />
            )}

            {/* 循环定时发布配置 - 当发布时间类型为循环时显示 */}
            {timeType === TimeType.LOOP && (
              <LoopPublishSection
                control={control}
              />
            )}
          </div>
        </div>

        <Separator />

        {/* 提交按钮 */}
        <div className="flex justify-end gap-4">
          <Button
            type="button"
            onClick={() => handleSaveToDraft() }
          >
            {currentDraftId ? '更新草稿' : '保存草稿箱'}
          </Button>

          <Button
            type="button"
            variant="outline"
            onClick={() => {
              reset()
              setSelectedAccounts([])
              setSelectedVideos([])
            }}
          >
            重置
          </Button>
          <Button
            type="submit"
            disabled={submitPublishMutation.isPending}
            className="min-w-[120px]"
          >
            {submitPublishMutation.isPending ? '发布中...' : '立即发布'}
          </Button>
        </div>
      </form>
    </div>
  )
}
