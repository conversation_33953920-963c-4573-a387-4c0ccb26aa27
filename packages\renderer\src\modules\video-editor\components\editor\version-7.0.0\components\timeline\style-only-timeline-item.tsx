import React, { FC, ReactNode } from 'react'
import { Overlay, OverlayType } from '@app/rve-shared/types'
import { TimelineItemLabel } from './timeline-item-label'
import clsx from 'clsx'

export interface TimelineItemContainerProps {
  /** 要显示的覆盖层对象 */
  item: Overlay
  /** 容器宽度（像素） */
  width: number
  /** 容器高度（像素，可选） */
  height?: number
  /** 是否显示标签 */
  showLabel?: boolean
  /** 子元素内容 */
  children?: ReactNode
  /** 额外的 CSS 类名 */
  className?: string
}

/**
 * 根据覆盖层类型获取对应的样式类名
 * @param type 覆盖层类型
 * @param isHandle 是否为拖拽手柄样式
 * @returns 样式类名字符串
 */
function getItemClasses(
  type: OverlayType,
  isHandle: boolean = false,
): string {
  switch (type) {
    case OverlayType.TEXT:
      return isHandle
        ? 'bg-[#9E53E6] dark:bg-[#9E53E6]'
        : 'bg-[#9E53E6] hover:bg-[#9E53E6] border-[#9E53E6] text-[#9E53E6]'
    case OverlayType.VIDEO:
      return isHandle
        ? 'bg-white dark:bg-black'
        : 'bg-pink-400 border-slate-900 dark:border-white text-white dark:text-black'
    case OverlayType.SOUND:
      return isHandle
        ? 'bg-[#E49723] dark:bg-[#E49723]'
        : 'bg-[#E49723] hover:bg-[#E49723] border-[#E49723] text-[#E49723]'
    case OverlayType.CAPTION:
      return isHandle
        ? 'bg-blue-500'
        : 'bg-blue-500/20 hover:bg-blue-500/30 border-blue-500 text-blue-700'
    case OverlayType.STICKER:
      return isHandle
        ? 'bg-red-500 dark:bg-red-500'
        : 'bg-red-500 hover:bg-red-500 dark:bg-red-500 dark:hover:bg-red-500 border-red-500 dark:border-red-500 text-red-500 dark:text-white'
    default:
      return isHandle
        ? 'bg-gray-200 dark:bg-gray-700'
        : 'bg-gray-100 hover:bg-gray-200 dark:bg-gray-600 dark:hover:bg-gray-500 border-gray-300 dark:border-gray-400 text-gray-950 dark:text-white'
  }
}

/**
 * 时间轴项目容器组件
 * 负责渲染时间轴项目的外观样式，包括背景、边框、标签等
 * 使用相对定位，适合在各种布局中复用
 */
export const StyleOnlyTimelineItem: FC<TimelineItemContainerProps> = ({
  item,
  width,
  height,
  children,
  className,
}) => {
  return (
    <div
      className={clsx(
        'relative rounded-md cursor-pointer group overflow-hidden',
        getItemClasses(item.type),
        className
      )}
      style={{
        width,
        height,
        transition: 'opacity 0.2s',
      }}
    >
      {/* 标签显示 */}
      <TimelineItemLabel item={item} />

      {/* 子元素内容 */}
      {children}
    </div>
  )
}
